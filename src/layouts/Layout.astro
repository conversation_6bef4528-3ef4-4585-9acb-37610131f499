---
import '@fontsource-variable/inter';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import ThemeScript from '../components/ThemeScript.astro';

export interface Props {
  title: string;
  description?: string;
  ogImage?: string;
}

const { 
  title, 
  description = "Next-generation RCS solutions for businesses and developers",
  ogImage = "/images/og-image.jpg" 
} = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title} | RCS Tech Provider</title>
    <meta name="description" content={description} />
    
    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:url" content={Astro.url} />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={ogImage} />

    <!-- Comprehensive JSON-LD Schema -->
    <script type="application/ld+json" is:inline>
      {
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "Organization",
            "@id": "https://rcsplatform.com/#organization",
            "name": "RCS Platform",
            "url": "https://rcsplatform.com",
            "logo": {
              "@type": "ImageObject",
              "url": "https://rcsplatform.com/logo.png",
              "width": 200,
              "height": 60
            },
            "description": "Leading provider of Rich Communication Services (RCS) messaging solutions for businesses",
            "foundingDate": "2024",
            "industry": "Software Technology",
            "numberOfEmployees": "11-50",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "US"
            },
            "contactPoint": {
              "@type": "ContactPoint",
              "contactType": "customer service",
              "email": "<EMAIL>"
            },
            "sameAs": [
              "https://linkedin.com/company/rcsplatform",
              "https://twitter.com/rcsplatform"
            ]
          },
          {
            "@type": "WebSite",
            "@id": "https://rcsplatform.com/#website",
            "url": "https://rcsplatform.com",
            "name": "RCS Platform",
            "description": "Next-generation RCS messaging platform for businesses",
            "publisher": {
              "@id": "https://rcsplatform.com/#organization"
            },
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://rcsplatform.com/search?q={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          },
          {
            "@type": "SoftwareApplication",
            "@id": "https://rcsplatform.com/#software",
            "name": "RCS Business Messaging Platform",
            "description": "Comprehensive RCS messaging platform with AI analytics, message studio, and real-time engagement tools",
            "url": "https://rcsplatform.com",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": ["Web", "iOS", "Android"],
            "softwareVersion": "2.0",
            "releaseNotes": "Enhanced AI analytics and message studio features",
            "provider": {
              "@id": "https://rcsplatform.com/#organization"
            },
            "offers": {
              "@type": "Offer",
              "name": "RCS Platform Early Access",
              "description": "Join our waiting list for early access to the platform",
              "price": "0",
              "priceCurrency": "USD",
              "availability": "https://schema.org/PreOrder",
              "validFrom": "2024-01-01",
              "priceValidUntil": "2024-12-31"
            },
            "featureList": [
              "Rich Communication Services (RCS)",
              "Interactive Message Builder",
              "AI-Powered Analytics",
              "Real-time Engagement Tracking",
              "Message Studio with Templates",
              "Targeted Rewards System",
              "Smart Customer Segmentation",
              "Predictive Analytics",
              "Multi-channel Integration",
              "Enterprise Security & Compliance"
            ],
            "screenshot": "https://rcsplatform.com/screenshots/dashboard.png",
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "reviewCount": "150",
              "bestRating": "5",
              "worstRating": "1"
            }
          },
          {
            "@type": "WebPage",
            "@id": "https://rcsplatform.com/#webpage",
            "url": "https://rcsplatform.com",
            "name": "RCS Platform - Next-Generation Business Messaging",
            "description": "Transform your business communications with Rich Communication Services. Create interactive, engaging messages that drive results.",
            "isPartOf": {
              "@id": "https://rcsplatform.com/#website"
            },
            "about": {
              "@id": "https://rcsplatform.com/#software"
            },
            "primaryImageOfPage": {
              "@type": "ImageObject",
              "url": "https://rcsplatform.com/hero-image.png",
              "width": 1200,
              "height": 630
            },
            "datePublished": "2024-01-01",
            "dateModified": "2024-12-01"
          },
          {
            "@type": "Service",
            "@id": "https://rcsplatform.com/#service",
            "name": "RCS Messaging Solutions",
            "description": "Enterprise-grade RCS messaging services with AI analytics and interactive message capabilities",
            "provider": {
              "@id": "https://rcsplatform.com/#organization"
            },
            "serviceType": "Business Communication Platform",
            "areaServed": "Worldwide",
            "hasOfferCatalog": {
              "@type": "OfferCatalog",
              "name": "RCS Platform Services",
              "itemListElement": [
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "RCS Message Studio",
                    "description": "Visual drag-and-drop message builder with templates"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "AI Analytics Dashboard",
                    "description": "Real-time analytics with AI-powered insights and recommendations"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Interactive Messaging",
                    "description": "Rich media messaging with buttons, carousels, and quick replies"
                  }
                }
              ]
            }
          }
        ]
      }
    </script>

    <ThemeScript />
  </head>
  <body class="min-h-screen bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100 flex flex-col">
    <Header />
    <main class="flex-grow">
      <slot />
    </main>
    <Footer />
  </body>
</html>

<style is:global>
  :root {
    --font-sans: 'Inter Variable', system-ui, sans-serif;
    --font-mono: 'SF Mono', monospace;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: var(--font-sans);
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
  }
  
  /* Add smooth reveal animations for sections */
  .reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }
  
  .reveal.active {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Add focus styles for accessibility */
  :focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
</style>

<script>
  // Reveal animations for sections
  document.addEventListener('DOMContentLoaded', () => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('active');
        }
      });
    }, observerOptions);
    
    document.querySelectorAll('.reveal').forEach(el => {
      observer.observe(el);
    });
  });
</script>