---
import '@fontsource-variable/inter';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import ThemeScript from '../components/ThemeScript.astro';

export interface Props {
  title: string;
  description?: string;
  ogImage?: string;
}

const { 
  title, 
  description = "Next-generation RCS solutions for businesses and developers",
  ogImage = "/images/og-image.jpg" 
} = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title} | RCS Tech Provider</title>
    <meta name="description" content={description} />
    
    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:url" content={Astro.url} />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={ogImage} />

    <!-- JSON-LD Schema -->
    <script type="application/ld+json" is:inline>
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "RCS Platform",
        "description": "Next-generation RCS messaging platform for businesses",
        "url": "https://rcsplatform.com",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        "provider": {
          "@type": "Organization",
          "name": "RCS Platform",
          "url": "https://rcsplatform.com"
        },
        "featureList": [
          "Rich Communication Services",
          "Interactive Messaging",
          "AI Analytics",
          "Message Studio",
          "Real-time Analytics"
        ]
      }
    </script>

    <ThemeScript />
  </head>
  <body class="min-h-screen bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100 flex flex-col">
    <Header />
    <main class="flex-grow">
      <slot />
    </main>
    <Footer />
  </body>
</html>

<style is:global>
  :root {
    --font-sans: 'Inter Variable', system-ui, sans-serif;
    --font-mono: 'SF Mono', monospace;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: var(--font-sans);
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
  }
  
  /* Add smooth reveal animations for sections */
  .reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }
  
  .reveal.active {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Add focus styles for accessibility */
  :focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
</style>

<script>
  // Reveal animations for sections
  document.addEventListener('DOMContentLoaded', () => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('active');
        }
      });
    }, observerOptions);
    
    document.querySelectorAll('.reveal').forEach(el => {
      observer.observe(el);
    });
  });
</script>