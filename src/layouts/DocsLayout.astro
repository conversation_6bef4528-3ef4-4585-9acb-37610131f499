---
import Layout from './Layout.astro';
import DocsSidebar from '../components/docs/DocsSidebar.astro';
import TableOfContents from '../components/docs/TableOfContents.astro';

export interface Props {
  title: string;
  description?: string;
  toc?: any[];
}

const { 
  title, 
  description,
  toc = []
} = Astro.props;
---

<Layout title={title} description={description}>
  <div class="docs-container max-w-8xl mx-auto px-4 py-8 md:py-16 flex flex-col md:flex-row">
    <DocsSidebar />
    
    <div class="docs-content flex-1 md:ml-64 lg:mr-64">
      <article class="prose prose-lg dark:prose-invert max-w-3xl mx-auto">
        <h1>{title}</h1>
        <slot />
      </article>
    </div>
    
    {toc.length > 0 && (
      <div class="toc hidden lg:block lg:w-64 sticky top-24 self-start ml-8">
        <TableOfContents toc={toc} />
      </div>
    )}
  </div>
</Layout>

<style>
  .docs-container {
    min-height: calc(100vh - 180px);
  }
  
  @media (max-width: 768px) {
    .docs-content {
      margin-left: 0;
    }
  }
  
  :global(.prose h1) {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
  }
  
  :global(.prose h2) {
    font-size: 1.75rem;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  :global(.prose h3) {
    font-size: 1.375rem;
    margin-top: 2rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
  }
  
  :global(.prose p) {
    margin-bottom: 1.25rem;
  }
  
  :global(.prose code) {
    font-family: var(--font-mono);
    font-size: 0.9em;
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  :global(.dark .prose code) {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  :global(.prose pre) {
    padding: 1.25rem;
    border-radius: 0.5rem;
    overflow-x: auto;
  }
</style>