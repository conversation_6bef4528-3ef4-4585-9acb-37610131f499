---
import Layout from './Layout.astro';

export interface Props {
  title: string;
  description?: string;
  publishDate: string;
  author: string;
  authorImage?: string;
  category?: string;
  heroImage?: string;
}

const { 
  title, 
  description, 
  publishDate,
  author,
  authorImage = "/images/avatars/default.jpg",
  category = "Technology",
  heroImage
} = Astro.props;

// Format date
const formattedDate = new Date(publishDate).toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
---

<Layout title={title} description={description}>
  <article class="max-w-4xl mx-auto px-4 py-8 md:py-16">
    {heroImage && (
      <div class="mb-8 rounded-xl overflow-hidden">
        <img 
          src={heroImage} 
          alt={title} 
          class="w-full h-[400px] object-cover"
        />
      </div>
    )}
    
    <header class="mb-12">
      <div class="text-sm font-medium text-primary-600 dark:text-primary-400 mb-2">
        {category}
      </div>
      <h1 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
        {title}
      </h1>
      <div class="flex items-center">
        <img 
          src={authorImage} 
          alt={author} 
          class="w-12 h-12 rounded-full mr-4"
        />
        <div>
          <div class="font-medium">{author}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            {formattedDate} • 5 min read
          </div>
        </div>
      </div>
    </header>
    
    <div class="prose prose-lg dark:prose-invert max-w-none">
      <slot />
    </div>
    
    <div class="mt-16 pt-8 border-t border-gray-200 dark:border-gray-800">
      <h3 class="text-xl font-bold mb-4">Share this article</h3>
      <div class="flex space-x-4">
        <a href="#" class="text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
          <span class="sr-only">Twitter</span>
          <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
          </svg>
        </a>
        <a href="#" class="text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
          <span class="sr-only">LinkedIn</span>
          <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
          </svg>
        </a>
        <a href="#" class="text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
          <span class="sr-only">Facebook</span>
          <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
          </svg>
        </a>
      </div>
    </div>
  </article>
</Layout>

<style>
  :global(.prose img) {
    border-radius: 0.5rem;
    margin: 2rem 0;
  }
  
  :global(.prose blockquote) {
    border-left: 4px solid theme('colors.primary.500');
    padding-left: 1.5rem;
    font-style: italic;
    color: theme('colors.gray.600');
  }
  
  :global(.dark .prose blockquote) {
    color: theme('colors.gray.400');
  }
</style>