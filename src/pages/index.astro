---
import Layout from '../layouts/Layout.astro';
import Hero from '../components/home/<USER>';
import SocialProof from '../components/home/<USER>';
import Features from '../components/home/<USER>';
import RichMessageExamples from '../components/home/<USER>';
import MessageStudio from '../components/home/<USER>';
import AIAnalytics from '../components/home/<USER>';
import UseCases from '../components/home/<USER>';
import TechnicalOverview from '../components/home/<USER>';
import HowItWorks from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import CallToAction from '../components/home/<USER>';
import FAQ from '../components/home/<USER>';
import WaitingListModal from '../components/WaitingListModal.astro';
---

<Layout title="RCS Technology Provider - Next-Generation Business Messaging">
  <Hero />
  <SocialProof />
  <Features />
  <RichMessageExamples />
  <MessageStudio />
  <AIAnalytics />
  <UseCases />
  <TechnicalOverview />
  <HowItWorks />
  <Testimonials />
  <FAQ />
  <CallToAction />

  <!-- Add a button to open the waiting list modal -->
  <section class="py-16 text-center">
    <button id="openWaitingListModal" class="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-full text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
      Join Our Waiting List
    </button>
  </section>

  <!-- Include the Waiting List Modal component -->
  <WaitingListModal />
</Layout>