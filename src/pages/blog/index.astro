---
import Layout from '../../layouts/Layout.astro';

// In a real application, this would be fetched from a CMS or database
const posts = [
  {
    id: 1,
    title: 'The Future of Business Messaging with RCS',
    excerpt: 'Learn how RCS is transforming business messaging and providing new opportunities for customer engagement.',
    publishDate: '2025-02-15',
    author: '<PERSON>',
    authorImage: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    category: 'Technology',
    image: 'https://images.pexels.com/photos/7956037/pexels-photo-7956037.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    slug: 'future-of-business-messaging'
  },
  {
    id: 2,
    title: 'How to Increase Customer Engagement with Interactive RCS Messages',
    excerpt: 'Discover strategies for creating interactive RCS messages that drive customer engagement and conversions.',
    publishDate: '2025-02-10',
    author: '<PERSON>',
    authorImage: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    category: 'Marketing',
    image: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    slug: 'increase-customer-engagement'
  },
  {
    id: 3,
    title: 'RCS vs. SMS: What\'s the Difference?',
    excerpt: 'A comprehensive comparison of RCS and SMS messaging technologies and their business applications.',
    publishDate: '2025-02-05',
    author: 'Sarah Chen',
    authorImage: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    category: 'Technology',
    image: 'https://images.pexels.com/photos/6177645/pexels-photo-6177645.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    slug: 'rcs-vs-sms'
  }
];

// Format date
function formatDate(dateString: Date) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
---

<Layout title="Blog - RCS Technology Provider" description="Latest news, articles, and updates about RCS technology and business messaging">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-20">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white">Latest Articles</h1>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Insights, tutorials, and updates from our team of RCS messaging experts
      </p>
    </div>
    
    <!-- Featured post -->
    {posts.length > 0 && (
      <div class="mb-16">
        <a href={`/blog/${posts[0].slug}`} class="block group">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div class="rounded-xl overflow-hidden">
              <img 
                src={posts[0].image} 
                alt={posts[0].title} 
                class="w-full h-80 object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <div>
              <div class="text-sm font-medium text-primary-600 dark:text-primary-400 mb-2">
                {posts[0].category}
              </div>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                {posts[0].title}
              </h2>
              <p class="text-gray-600 dark:text-gray-400 mb-6">
                {posts[0].excerpt}
              </p>
              <div class="flex items-center">
                <img 
                  src={posts[0].authorImage} 
                  alt={posts[0].author} 
                  class="w-10 h-10 rounded-full mr-3 object-cover"
                />
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">{posts[0].author}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{formatDate(posts[0].publishDate)}</p>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    )}
    
    <!-- Remaining posts -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {posts.slice(1).map((post) => (
        <a href={`/blog/${post.slug}`} class="group">
          <article class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 h-full flex flex-col">
            <div class="overflow-hidden">
              <img 
                src={post.image} 
                alt={post.title} 
                class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <div class="p-6 flex-1 flex flex-col">
              <div class="text-sm font-medium text-primary-600 dark:text-primary-400 mb-2">
                {post.category}
              </div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                {post.title}
              </h2>
              <p class="text-gray-600 dark:text-gray-400 mb-6 flex-1">
                {post.excerpt}
              </p>
              <div class="flex items-center mt-auto">
                <img 
                  src={post.authorImage} 
                  alt={post.author} 
                  class="w-8 h-8 rounded-full mr-3 object-cover"
                />
                <div>
                  <p class="font-medium text-gray-900 dark:text-white text-sm">{post.author}</p>
                  <p class="text-xs text-gray-600 dark:text-gray-400">{formatDate(post.publishDate)}</p>
                </div>
              </div>
            </div>
          </article>
        </a>
      ))}
    </div>
    
    <!-- Newsletter signup -->
    <div class="mt-20 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-900 dark:to-secondary-900 rounded-xl p-8 md:p-12">
      <div class="max-w-3xl mx-auto text-center">
        <h3 class="text-2xl font-bold text-white mb-4">Stay updated with our newsletter</h3>
        <p class="text-white/90 mb-8">
          Get the latest news, articles, and resources, sent straight to your inbox every month.
        </p>
        <form class="max-w-md mx-auto flex flex-col sm:flex-row gap-3">
          <input 
            type="email" 
            placeholder="Enter your email" 
            class="px-4 py-3 rounded-md flex-1 text-gray-900 border-0 focus:ring-2 focus:ring-white"
          />
          <button 
            type="submit"
            class="px-6 py-3 rounded-md bg-white text-primary-700 font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600"
          >
            Subscribe
          </button>
        </form>
        <p class="mt-4 text-sm text-white/80">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </div>
  </div>
</Layout>