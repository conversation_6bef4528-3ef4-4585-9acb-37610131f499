---
import DocsLayout from '../../layouts/DocsLayout.astro';
import { Image } from 'astro:assets';

// Import images
import promoImage from '../../assets/rcs/promotional-preview.svg';
import locationImage from '../../assets/rcs/location-preview.svg';
import appointmentImage from '../../assets/rcs/appointment-preview.svg';
import feedbackImage from '../../assets/rcs/feedback-preview.svg';

const toc = [
  { text: 'Introduction', slug: 'introduction', depth: 2 },
  { text: 'What is RCS?', slug: 'what-is-rcs', depth: 2 },
  { text: 'Key Features', slug: 'key-features', depth: 2 },
  { text: 'Rich Media', slug: 'rich-media', depth: 3 },
  { text: 'Interactive Elements', slug: 'interactive-elements', depth: 3 },
  { text: 'Typing Indicators', slug: 'typing-indicators', depth: 3 },
  { text: 'Read Receipts', slug: 'read-receipts', depth: 3 },
  { text: 'Use Cases', slug: 'use-cases', depth: 2 }
];
---

<style>
  .use-case-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .use-case-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
  }

  .preview-container {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
  }

  .dark .preview-container {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  .phone-frame {
    position: relative;
    aspect-ratio: 9/16;
    max-width: 280px;
    margin: 0 auto;
    border-radius: 24px;
    padding: 12px;
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .dark .phone-frame {
    background: #1e293b;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  }

  .feature-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-primary-200) 100%);
  }

  .dark .feature-icon {
    background: linear-gradient(135deg, var(--color-primary-900) 0%, var(--color-primary-800) 100%);
  }
</style>

<DocsLayout title="Documentation" description="Learn how to integrate and use our RCS platform" toc={toc}>
  <h2 id="introduction">Introduction</h2>
  <p>
    Welcome to the RCS Platform documentation. Here you'll find comprehensive guides and documentation to help you start working with our RCS platform as quickly as possible.
  </p>
  
  <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <a href="/docs/quickstart" class="block p-6 bg-gray-50 dark:bg-gray-800 rounded-xl hover:shadow-md transition-shadow duration-300">
      <h3 class="text-lg font-semibold mb-2">Quick Start Guide</h3>
        <p class="text-gray-600 dark:text-gray-400">Get up and running with RCS Platform in minutes</p>
    </a>
    <a href="/docs/api/messages" class="block p-6 bg-gray-50 dark:bg-gray-800 rounded-xl hover:shadow-md transition-shadow duration-300">
      <h3 class="text-lg font-semibold mb-2">API Reference</h3>
      <p class="text-gray-600 dark:text-gray-400">Detailed documentation for our API endpoints</p>
    </a>
  </div>
  
  <h2 id="what-is-rcs" class="mt-12">What is RCS?</h2>
  <p>
    Rich Communication Services (RCS) is a communication protocol between mobile telephone carriers and between phone and carrier, aiming to replace SMS messages with a text-message system that is richer and can transmit in-call multimedia.
  </p>
  <p class="mt-4">
    RCS enables businesses to send rich, interactive messages to customers through their default messaging app, without requiring them to download additional applications. This creates a seamless, app-like experience directly in the messaging interface.
  </p>
  
  <h2 id="key-features" class="mt-12">Key Features</h2>
  <p>
    Our RCS platform provides a comprehensive suite of features that enable businesses to create engaging messaging experiences:
  </p>
  
  <h3 id="rich-media" class="mt-6">Rich Media</h3>
  <p>
    Send high-resolution images, videos, GIFs, and audio messages to create visually engaging conversations with customers.
  </p>
  
  <div class="mt-4 bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
    <pre class="text-sm overflow-x-auto"><code class="language-javascript">{`// Example: Sending a message with an image
const message = rcsClient.createMessage({
  recipient: '+***********',
  content: {
    text: 'Check out our new product!',
    media: {
      type: 'image',
      url: 'https://example.com/product.jpg',
      altText: 'New product image'
    }
  }
});`}</code></pre>
  </div>
  
  <h3 id="interactive-elements" class="mt-8">Interactive Elements</h3>
  <p>
    Add buttons, suggested replies, and carousels to your messages to drive customer engagement and conversions.
  </p>
  
  <div class="mt-4 bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
    <pre class="text-sm overflow-x-auto"><code class="language-javascript">{`// Example: Adding interactive buttons
const message = rcsClient.createMessage({
  recipient: '+***********',
  content: {
    text: 'Would you like to make a reservation?',
    buttons: [
      {
        type: 'reply',
        text: 'Yes, please'
      },
      {
        type: 'reply',
        text: 'No, thanks'
      },
      {
        type: 'url',
        text: 'View Menu',
        url: 'https://example.com/menu'
      }
    ]
  }
});`}</code></pre>
  </div>
  
  <h3 id="typing-indicators" class="mt-8">Typing Indicators</h3>
  <p>
    Show customers when agents are typing, creating a more conversational and engaging experience.
  </p>
  
  <h3 id="read-receipts" class="mt-8">Read Receipts</h3>
  <p>
    Track when messages are delivered and read, providing valuable insights into customer engagement.
  </p>
  
  <h2 id="use-cases" class="mt-12">Business Use Cases</h2>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
    <!-- Promotional Campaign Example -->
    <div class="use-case-card bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
      <div class="feature-icon">
        <span class="text-2xl">💰</span>
      </div>
      <h3 class="text-xl font-semibold mb-4">Promotional Campaigns</h3>
      
      <!-- Start: Visual Component for Promotional Campaigns -->
      <div class="relative w-full max-w-xs sm:max-w-sm mx-auto mt-6 rounded-xl border-8 border-gray-900 dark:border-gray-700 shadow-lg overflow-hidden bg-white dark:bg-gray-800">
        <div class="p-4 space-y-3">
          <!-- Carousel Placeholder -->
          <div class="w-full aspect-video bg-gradient-to-br from-primary-400 to-blue-500 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
            Product Carousel Preview
          </div>
          <!-- Card 1 -->
          <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
            <div class="w-full h-24 bg-blue-100 dark:bg-blue-800 rounded-md mb-2"></div>
            <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">Awesome Product</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Check out the latest features!</p>
          </div>
           <!-- Card 2 -->
          <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
            <div class="w-full h-24 bg-blue-100 dark:bg-blue-800 rounded-md mb-2"></div>
            <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">Another Great Item</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Limited time offer!</p>
          </div>
          <!-- Suggested Reply Buttons -->
          <div class="flex gap-2 mt-3">
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">View Details</button>
            <button class="flex-1 text-center text-white bg-primary-600 hover:bg-primary-700 px-3 py-1.5 rounded-md text-sm font-medium">Shop Now</button>
          </div>
        </div>
      </div>
      <!-- End: Visual Component for Promotional Campaigns -->
  
      <div class="mt-6 space-y-2">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">What this shows:</h4>
        <p class="text-gray-600 dark:text-gray-400">
          This visual represents an interactive promotional message. It features a rich media carousel highlighting different products. Below the carousel, suggested reply buttons allow customers to quickly view product details or make a purchase directly from the message.
        </p>
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Key Features:</h4>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400">
          <li>Rich media carousels</li>
          <li>Interactive product catalogs</li>
          <li>One-click shopping</li>
          <li>Suggested replies</li>
        </ul>
      </div>
    </div>

    <!-- Location Services Example -->
    <div class="use-case-card bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
      <div class="feature-icon">
        <span class="text-2xl">📍</span>
      </div>
      <h3 class="text-xl font-semibold mb-4">Location Services</h3>
      
      <!-- Start: Visual Component for Location Services -->
      <div class="relative w-full max-w-xs sm:max-w-sm mx-auto mt-6 rounded-xl border-8 border-gray-900 dark:border-gray-700 shadow-lg overflow-hidden bg-white dark:bg-gray-800">
        <div class="p-4 space-y-3">
           <!-- Map Placeholder -->
          <div class="relative w-full aspect-[4/3] bg-gradient-to-br from-green-400 to-teal-500 rounded-lg flex items-center justify-center text-white text-sm font-semibold">
            Map View Preview
             <div class="absolute inset-0 flex items-center justify-center">
               <div class="w-4 h-4 bg-red-600 rounded-full shadow-md"></div>
             </div>
          </div>
          <!-- Suggested Reply Buttons -->
          <div class="flex gap-2 mt-3">
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">Get Directions</button>
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">View Hours</button>
          </div>
        </div>
      </div>
      <!-- End: Visual Component for Location Services -->

      <div class="mt-6 space-y-2">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">What this shows:</h4>
        <p class="text-gray-600 dark:text-gray-400">
          This visual illustrates an RCS message for location services. It can include an interactive map allowing customers to view nearby store locations directly within the message. Buttons could provide options for turn-by-turn navigation or viewing business hours.
        </p>
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Key Features:</h4>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400">
          <li>Interactive maps</li>
          <li>Turn-by-turn navigation</li>
          <li>Store locator</li>
          <li>Share location</li>
        </ul>
      </div>
    </div>

    <!-- Appointment Scheduling Example -->
    <div class="use-case-card bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
      <div class="feature-icon">
        <span class="text-2xl">📅</span>
      </div>
      <h3 class="text-xl font-semibold mb-4">Smart Scheduling</h3>
      
      <!-- Start: Visual Component for Appointment Scheduling -->
      <div class="relative w-full max-w-xs sm:max-w-sm mx-auto mt-6 rounded-xl border-8 border-gray-900 dark:border-gray-700 shadow-lg overflow-hidden bg-white dark:bg-gray-800">
        <div class="p-4 space-y-3">
          <!-- Calendar Placeholder -->
          <div class="w-full aspect-[4/3] bg-gradient-to-br from-purple-400 to-indigo-500 rounded-lg flex flex-col items-center justify-center text-white text-sm font-semibold p-4">
            <div class="text-lg font-bold mb-2">Book an Appointment</div>
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="text-sm text-white/80">Select a date and time</div>
          </div>
          <!-- Suggested Reply Buttons -->
          <div class="flex gap-2 mt-3">
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">View Calendar</button>
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">Call to Book</button>
          </div>
        </div>
      </div>
      <!-- End: Visual Component for Appointment Scheduling -->

      <div class="mt-6 space-y-2">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">What this shows:</h4>
        <p class="text-gray-600 dark:text-gray-400">
          This visual demonstrates how RCS can be used for appointment scheduling. A message could include a calendar view for selecting a time slot, followed by buttons to confirm, reschedule, or cancel the appointment directly within the chat.
        </p>
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Key Features:</h4>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400">
          <li>Calendar integration</li>
          <li>Automated reminders</li>
          <li>One-click rescheduling</li>
          <li>Confirmation/Cancellation options</li>
        </ul>
      </div>
    </div>

    <!-- Customer Feedback Example -->
    <div class="use-case-card bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
      <div class="feature-icon">
        <span class="text-2xl">⭐</span>
      </div>
      <h3 class="text-xl font-semibold mb-4">Customer Feedback</h3>
      
      <!-- Start: Visual Component for Customer Feedback -->
      <div class="relative w-full max-w-xs sm:max-w-sm mx-auto mt-6 rounded-xl border-8 border-gray-900 dark:border-gray-700 shadow-lg overflow-hidden bg-white dark:bg-gray-800">
        <div class="p-4 space-y-3">
          <!-- Feedback Request -->
          <div class="bg-pink-50 dark:bg-pink-900/30 rounded-lg p-3">
            <p class="text-sm text-gray-900 dark:text-white mb-2 font-medium">Rate your experience:</p>
            <!-- Rating Stars -->
            <div class="flex items-center justify-center gap-1 text-yellow-400">
              <span class="text-lg">★</span>
              <span class="text-lg">★</span>
              <span class="text-lg">★</span>
              <span class="text-gray-400 dark:text-gray-600 text-lg">★</span>
              <span class="text-gray-400 dark:text-gray-600 text-lg">★</span>
            </div>
          </div>
           <!-- Text Input Placeholder -->
          <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
             <p class="text-sm text-gray-600 dark:text-gray-400">Leave a comment...</p>
          </div>
          <!-- Suggested Reply Buttons -->
          <div class="flex gap-2 mt-3">
            <button class="flex-1 text-center text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/40 px-3 py-1.5 rounded-md text-sm font-medium">Submit Feedback</button>
          </div>
        </div>
      </div>
      <!-- End: Visual Component for Customer Feedback -->

      <div class="mt-6 space-y-2">
        <h4 class="font-medium text-gray-900 dark:text-gray-100">What this shows:</h4>
        <p class="text-gray-600 dark:text-gray-400">
          This visual illustrates how RCS can be used to collect customer feedback. A message can include a rating request with interactive stars or quick reply buttons for submitting feedback directly within the messaging interface.
        </p>
        <h4 class="font-medium text-gray-900 dark:text-gray-100">Key Features:</h4>
        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400">
          <li>Quick reactions</li>
          <li>Interactive surveys</li>
          <li>Rating systems</li>
          <li>Rich feedback forms</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Feature Highlights -->
  <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-3">
        <span class="text-2xl">📊</span><br/>
        Analytics & Insights
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        Track message delivery, engagement rates, and customer interactions in real-time.
      </p>
    </div>

    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-3">
        <span class="text-2xl">🔒</span><br/>
        Enterprise Security
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        End-to-end encryption and compliance with global messaging standards.
      </p>
    </div>

    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
      <h3 class="text-lg font-semibold mb-3">
        <span class="text-2xl">🔄</span><br/>
        Seamless Integration
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        Easy integration with your existing CRM and business systems.
      </p>
    </div>
  </div>

  <!-- Call to Action -->
  <div class="mt-12 p-8 border border-primary-200 dark:border-primary-800 rounded-xl bg-primary-50 dark:bg-primary-900/20">
    <h3 class="text-xl font-semibold mb-4">Ready to Transform Your Business Communication?</h3>
    <p class="mb-6 text-gray-600 dark:text-gray-400">
      Join thousands of businesses already using RCS to enhance their customer engagement and drive growth.
    </p>
    <div class="flex flex-wrap gap-4">
    <a 
      href="/docs/quickstart" 
        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200"
    >
        Get Started Free
      </a>
      <a 
        href="/docs/api/messages" 
        class="inline-flex items-center px-6 py-3 border border-primary-600 text-base font-medium rounded-md text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/40 transition-colors duration-200"
      >
        View API Docs
    </a>
    </div>
  </div>
</DocsLayout>