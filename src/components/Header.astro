---
import ThemeToggle from './ThemeToggle.astro';
---

<header class="sticky top-0 z-50 w-full border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo and navigation -->
      <div class="flex items-center">
        <a href="/" class="flex items-center text-primary-600 dark:text-primary-400">
          <span class="text-2xl font-bold">RCS Platform</span>
        </a>
        <nav class="hidden md:ml-10 md:flex md:space-x-8">
          <a href="/" class="font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Home</a>
          <a href="/products" class="font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Products</a>
          <a href="/docs" class="font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Documentation</a>
          <a href="/blog" class="font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Blog</a>
          <a href="/contact" class="font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Contact</a>
        </nav>
      </div>
      
      <!-- Right side buttons -->
      <div class="flex items-center">
        <ThemeToggle />
        
        <a 
          href="/get-started" 
          class="ml-6 hidden md:inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Get Started
        </a>
        
        <!-- Mobile menu button -->
        <button 
          type="button" 
          class="md:hidden ml-4 text-gray-500 dark:text-gray-400"
          aria-label="Toggle menu"
          x-data="{}"
          @click="$store.mobileMenu.toggle()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Mobile menu -->
  <div 
    x-data="{}"
    x-show="$store.mobileMenu.isOpen"
    x-transition:enter="transition ease-out duration-200"
    x-transition:enter-start="opacity-0 -translate-y-4"
    x-transition:enter-end="opacity-100 translate-y-0"
    x-transition:leave="transition ease-in duration-150"
    x-transition:leave-start="opacity-100 translate-y-0"
    x-transition:leave-end="opacity-0 -translate-y-4"
    class="md:hidden"
    style="display: none;"
  >
    <div class="pt-2 pb-4 px-4 space-y-1 sm:px-6">
      <a href="/" class="block font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Home</a>
      <a href="/products" class="block font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Products</a>
      <a href="/docs" class="block font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Documentation</a>
      <a href="/blog" class="block font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Blog</a>
      <a href="/contact" class="block font-medium text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-400 px-3 py-2 rounded-md">Contact</a>
      <a href="/get-started" class="block w-full mt-4 px-4 py-2 text-center text-white bg-primary-600 hover:bg-primary-700 rounded-md">Get Started</a>
    </div>
  </div>
</header>

<script>
  import Alpine from 'alpinejs';
  
  // Store for managing mobile menu state
  document.addEventListener('alpine:init', () => {
    Alpine.store('mobileMenu', {
      isOpen: false,
      toggle() {
        this.isOpen = !this.isOpen;
      },
      close() {
        this.isOpen = false;
      }
    });
  });
  
  // Close menu when clicking outside
  document.addEventListener('click', (event) => {
    const menu = document.querySelector('[x-data="{}"][x-show="$store.mobileMenu.isOpen"]');
    const button = document.querySelector('[x-data="{}"][\\@click="$store.mobileMenu.toggle()"]');
    
    if (menu && button && !menu.contains(event.target) && !button.contains(event.target)) {
      Alpine.store('mobileMenu').close();
    }
  });
</script>