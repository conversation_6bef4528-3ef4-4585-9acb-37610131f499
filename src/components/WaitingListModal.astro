<div id="waitingListModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 dark:bg-opacity-75 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

    <!-- Modal panel -->
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

    <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-200 dark:border-gray-700">
      <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="modal-title">
                  Join Our Waiting List
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Be the first to experience the future of RCS messaging
                </p>
              </div>
              <button id="closeModalButton" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <form id="waitingListForm" class="mt-5 space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name *</label>
                  <input type="text" name="name" id="name" autocomplete="name" required class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-all duration-200">
                </div>

                <div>
                  <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company *</label>
                  <input type="text" name="company" id="company" autocomplete="organization" required class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-all duration-200">
                </div>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
                <input type="email" name="email" id="email" autocomplete="email" required class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-all duration-200">
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
                <input type="tel" name="phone" id="phone" autocomplete="tel" class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-all duration-200">
              </div>

              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tell us about your use case</label>
                <textarea id="message" name="message" rows="3" placeholder="What messaging challenges are you looking to solve?" class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none"></textarea>
              </div>
              
              <div class="bg-gray-50 dark:bg-gray-800 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse rounded-b-2xl -mx-4 -mb-4 sm:-mx-6 sm:-mb-4 mt-6">
                <button type="submit" class="w-full inline-flex justify-center rounded-xl border border-transparent shadow-sm px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-base font-medium text-white hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto transition-all duration-200 transform hover:-translate-y-0.5">
                  Join Waiting List
                </button>
                <button type="button" id="cancelModalButton" class="mt-3 w-full inline-flex justify-center rounded-xl border border-gray-300 dark:border-gray-600 shadow-sm px-6 py-3 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto transition-all duration-200">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Enhanced modal logic with hash routing and multiple button support
  const modal = document.getElementById('waitingListModal');
  const form = document.getElementById('waitingListForm') as HTMLFormElement;
  const closeModalButton = document.getElementById('closeModalButton');
  const cancelModalButton = document.getElementById('cancelModalButton');

  // Function to open modal
  function openModal() {
    if (modal) {
      modal.classList.remove('hidden');
      window.location.hash = 'join-waiting-list';
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
  }

  // Function to close modal
  function closeModal() {
    if (modal) {
      modal.classList.add('hidden');
      if (window.location.hash === '#join-waiting-list') {
        history.replaceState(null, '', window.location.pathname);
      }
      document.body.style.overflow = ''; // Restore scrolling
    }
  }

  // Handle hash routing
  function handleHashChange() {
    if (window.location.hash === '#join-waiting-list') {
      openModal();
    } else {
      closeModal();
    }
  }

  // Listen for hash changes
  window.addEventListener('hashchange', handleHashChange);

  // Check hash on page load
  document.addEventListener('DOMContentLoaded', handleHashChange);

  // Add event listeners to all "Join Waiting List" buttons
  const joinButtons = [
    'heroJoinWaitingList',
    'headerJoinWaitingList',
    'ctaJoinWaitingList',
    'studioJoinWaitingList',
    'analyticsJoinWaitingList',
    'urgencyJoinWaitingList'
  ];

  joinButtons.forEach(buttonId => {
    const button = document.getElementById(buttonId);
    if (button) {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        openModal();
      });
    }
  });

  // Close modal event listeners
  if (closeModalButton) {
    closeModalButton.addEventListener('click', closeModal);
  }

  if (cancelModalButton) {
    cancelModalButton.addEventListener('click', closeModal);
  }

  // Close modal when clicking outside
  if (modal) {
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
  }

  // Close modal with Escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && !modal?.classList.contains('hidden')) {
      closeModal();
    }
  });

  // Ensure both form and modal exist before adding the submit listener
  if (form && modal) {
    form.addEventListener('submit', async (event) => {
      event.preventDefault();
      // *** BACKEND SUBMISSION LOGIC GOES HERE ***
      // You will need to send the form data to your backend.
      // Example using Fetch API:
      /*
      const formData = new FormData(form);
      const response = await fetch('/api/submit-waiting-list', {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        alert('Successfully joined the waiting list!');
        modal.classList.add('hidden'); // Close modal on success
      } else {
        alert('Failed to join the waiting list. Please try again.');
      }
      */
      
      // For demonstration, just log the data and close
      console.log('Form submitted:', Object.fromEntries(new FormData(form).entries()));
      alert('Form submitted (backend not implemented).');
      modal.classList.add('hidden');
      form.reset(); // Clear form fields
    });
  }
</script>

<style>
  /* Add any component-specific styles here if needed */
</style> 