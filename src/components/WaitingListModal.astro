<div id="waitingListModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 dark:bg-opacity-75 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

    <!-- Modal panel -->
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

    <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
              Join Our Waiting List
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Fill out the form below to join our waiting list for Customers or Partners.
              </p>
            </div>
            
            <form id="waitingListForm" class="mt-5 space-y-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                <input type="text" name="name" id="name" autocomplete="name" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
                <input type="email" name="email" id="email" autocomplete="email" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number (Optional)</label>
                <input type="tel" name="phone" id="phone" autocomplete="tel" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Company</label>
                <input type="text" name="company" id="company" autocomplete="organization" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Message (Optional)</label>
                <textarea id="message" name="message" rows="4" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"></textarea>
              </div>
              
              <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                  Submit
                </button>
                <button type="button" id="closeModalButton" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Basic modal toggle logic (can be enhanced with Astro.js or other methods)
  const openModalButton = document.getElementById('openWaitingListModal'); // You need an element with this ID to open the modal
  const closeModalButton = document.getElementById('closeModalButton');
  const modal = document.getElementById('waitingListModal');
  const form = document.getElementById('waitingListForm') as HTMLFormElement; // Explicitly cast to HTMLFormElement

  if (openModalButton && modal) {
    openModalButton.addEventListener('click', () => {
      modal.classList.remove('hidden');
    });
  }

  if (closeModalButton && modal) {
    closeModalButton.addEventListener('click', () => {
      modal.classList.add('hidden');
    });
  }

  // Ensure both form and modal exist before adding the submit listener
  if (form && modal) {
    form.addEventListener('submit', async (event) => {
      event.preventDefault();
      // *** BACKEND SUBMISSION LOGIC GOES HERE ***
      // You will need to send the form data to your backend.
      // Example using Fetch API:
      /*
      const formData = new FormData(form);
      const response = await fetch('/api/submit-waiting-list', {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        alert('Successfully joined the waiting list!');
        modal.classList.add('hidden'); // Close modal on success
      } else {
        alert('Failed to join the waiting list. Please try again.');
      }
      */
      
      // For demonstration, just log the data and close
      console.log('Form submitted:', Object.fromEntries(new FormData(form).entries()));
      alert('Form submitted (backend not implemented).');
      modal.classList.add('hidden');
      form.reset(); // Clear form fields
    });
  }
</script>

<style>
  /* Add any component-specific styles here if needed */
</style> 