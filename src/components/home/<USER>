---
// FAQ Component with Schema Markup for SEO
---

<!-- FAQ Schema -->
<script type="application/ld+json" is:inline>
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What is RCS messaging?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Rich Communication Services (RCS) is the next-generation messaging protocol that replaces SMS with rich, interactive messaging experiences. It supports high-resolution images, videos, interactive buttons, carousels, and real-time engagement tracking."
      }
    },
    {
      "@type": "Question",
      "name": "How does AI analytics improve messaging performance?",
      "acceptedAnswer": {
        "@type": "Answer", 
        "text": "Our AI analytics platform analyzes messaging patterns, customer behavior, and engagement data to provide actionable insights. It predicts optimal send times, suggests content improvements, identifies high-value customers for targeted rewards, and helps optimize conversion rates."
      }
    },
    {
      "@type": "Question",
      "name": "What devices and carriers support RCS?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "RCS is supported by major carriers worldwide and works on Android devices with Google Messages, Samsung Messages, and other RCS-enabled messaging apps. We provide fallback to SMS for unsupported devices to ensure 100% message delivery."
      }
    },
    {
      "@type": "Question",
      "name": "Is the platform secure and compliant?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, our platform is SOC 2 compliant, GDPR ready, and ISO 27001 certified. We use end-to-end encryption, enterprise-grade security measures, and maintain 99.9% uptime with comprehensive data protection."
      }
    },
    {
      "@type": "Question",
      "name": "How do I get started with the platform?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Join our waiting list to get early access to the platform. We're currently accepting a limited number of businesses into our early access program with priority onboarding, dedicated support, and special pricing."
      }
    },
    {
      "@type": "Question",
      "name": "What makes your Message Studio unique?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Our Message Studio features a visual drag-and-drop builder with pre-built templates, real-time preview, custom branding options, and multi-device testing. It allows businesses to create professional RCS messages without any coding knowledge."
      }
    }
  ]
}
</script>

<section class="py-20 bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Frequently Asked Questions
      </h2>
      <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Get answers to common questions about our RCS messaging platform
      </p>
    </div>

    <div class="max-w-4xl mx-auto">
      <div class="space-y-6">
        <!-- FAQ Item 1 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                What is RCS messaging?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Rich Communication Services (RCS) is the next-generation messaging protocol that replaces SMS with rich, interactive messaging experiences. It supports high-resolution images, videos, interactive buttons, carousels, and real-time engagement tracking.
            </p>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                How does AI analytics improve messaging performance?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Our AI analytics platform analyzes messaging patterns, customer behavior, and engagement data to provide actionable insights. It predicts optimal send times, suggests content improvements, identifies high-value customers for targeted rewards, and helps optimize conversion rates.
            </p>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                What devices and carriers support RCS?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              RCS is supported by major carriers worldwide and works on Android devices with Google Messages, Samsung Messages, and other RCS-enabled messaging apps. We provide fallback to SMS for unsupported devices to ensure 100% message delivery.
            </p>
          </div>
        </div>

        <!-- FAQ Item 4 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Is the platform secure and compliant?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Yes, our platform is SOC 2 compliant, GDPR ready, and ISO 27001 certified. We use end-to-end encryption, enterprise-grade security measures, and maintain 99.9% uptime with comprehensive data protection.
            </p>
          </div>
        </div>

        <!-- FAQ Item 5 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                How do I get started with the platform?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Join our waiting list to get early access to the platform. We're currently accepting a limited number of businesses into our early access program with priority onboarding, dedicated support, and special pricing.
            </p>
          </div>
        </div>

        <!-- FAQ Item 6 -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <button class="w-full px-8 py-6 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                What makes your Message Studio unique?
              </h3>
              <svg class="w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </button>
          <div class="hidden px-8 pb-6">
            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
              Our Message Studio features a visual drag-and-drop builder with pre-built templates, real-time preview, custom branding options, and multi-device testing. It allows businesses to create professional RCS messages without any coding knowledge.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="text-center mt-16">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Still have questions?</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-8">
        Join our waiting list and our team will reach out to answer any specific questions about your use case.
      </p>
      <button id="faqJoinWaitingList" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white font-semibold rounded-xl hover:from-primary-700 hover:to-primary-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
        Join Waiting List
        <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
        </svg>
      </button>
    </div>
  </div>
</section>

<script is:inline>
  function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('svg');
    
    if (content.classList.contains('hidden')) {
      content.classList.remove('hidden');
      icon.style.transform = 'rotate(180deg)';
    } else {
      content.classList.add('hidden');
      icon.style.transform = 'rotate(0deg)';
    }
  }
</script>
