---
import TicketSVG from '../svg/TicketSVG.astro';
import LocationMapSVG from '../svg/LocationMapSVG.astro';
import PromotionalSVG from '../svg/PromotionalSVG.astro';
import AppointmentSVG from '../svg/AppointmentSVG.astro';
import ShoppingSVG from '../svg/ShoppingSVG.astro';
---

<section class="relative overflow-hidden bg-gradient-to-b from-primary-50 to-white dark:from-gray-900 dark:to-gray-800 py-16 sm:py-24 md:py-32">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Text Column -->
      <div class="flex flex-col justify-center max-w-xl w-full mx-auto lg:mx-0">
        <h1 class="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
          Next-Generation Business Messaging
          <span class="text-primary-600 dark:text-primary-400">Platform</span>
        </h1>
        <p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
          Transform your customer engagement with rich, interactive messaging experiences. Reach customers where they are - in their default messaging app.
        </p>
        <div class="mt-8 flex items-center gap-x-6">
          <button
            id="heroJoinWaitingList"
            class="rounded-full bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-4 text-base font-semibold text-white shadow-lg hover:from-primary-700 hover:to-primary-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
          >
            Join Waiting List
          </button>
          <a href="/docs" class="text-base font-semibold leading-7 text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400">
            Documentation <span aria-hidden="true">→</span>
          </a>
        </div>
        <!-- Trust indicators -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Trusted by industry leaders</p>
          <div class="flex flex-wrap gap-8 items-center opacity-70">
            <div class="w-24 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <!-- More logos -->
          </div>
        </div>
      </div>
      <!-- Image Column -->
      <div class="relative flex items-center justify-center">
        <div class="relative w-full max-w-xs sm:max-w-md md:max-w-lg mx-auto" style="max-height:600px;">
          <!-- Interactive Demo Selector -->
          <div class="absolute -top-20 left-1/2 transform -translate-x-1/2 z-10 w-full max-w-lg">
            <div class="flex flex-wrap justify-center gap-2 bg-white dark:bg-gray-800 rounded-2xl p-3 shadow-xl border border-gray-200 dark:border-gray-700">
              <button id="demo-shopping" class="px-3 py-2 rounded-full text-xs font-medium bg-primary-600 text-white transition-all duration-200 hover:bg-primary-700">Shopping</button>
              <button id="demo-ticket" class="px-3 py-2 rounded-full text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 transition-all duration-200">Tickets</button>
              <button id="demo-promo" class="px-3 py-2 rounded-full text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 transition-all duration-200">Promos</button>
              <button id="demo-map" class="px-3 py-2 rounded-full text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 transition-all duration-200">Maps</button>
              <button id="demo-appointment" class="px-3 py-2 rounded-full text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 transition-all duration-200">Appointments</button>
            </div>
          </div>

          <div class="relative aspect-[9/16] rounded-3xl border-8 border-gray-900 dark:border-gray-700 shadow-2xl overflow-hidden bg-white dark:bg-gray-800">
            <!-- Demo Container -->
            <div id="demo-container" class="absolute inset-0 w-full h-full">
              <!-- Shopping Demo (Default) -->
              <div id="shopping-demo" class="demo-content absolute inset-0 w-full h-full">
                <ShoppingSVG
                  class="w-full h-full"
                  businessName="Fashion Store"
                  interactive={true}
                />
              </div>

              <!-- Ticket Demo -->
              <div id="ticket-demo" class="demo-content absolute inset-0 w-full h-full p-6 flex items-center justify-center hidden">
                <TicketSVG
                  class="w-full h-auto max-h-full"
                  eventName="SUMMER FESTIVAL"
                  eventDate="July 15, 2025"
                  eventLocation="Central Park"
                  ticketType="VIP Pass"
                  interactive={true}
                />
              </div>

              <!-- Promotional Demo -->
              <div id="promo-demo" class="demo-content absolute inset-0 w-full h-full p-6 flex items-center justify-center hidden">
                <PromotionalSVG
                  class="w-full h-auto max-h-full"
                  title="FLASH SALE"
                  subtitle="Limited Time"
                  discount="50% OFF"
                  theme="sale"
                  interactive={true}
                />
              </div>

              <!-- Map Demo -->
              <div id="map-demo" class="demo-content absolute inset-0 w-full h-full p-6 flex items-center justify-center hidden">
                <LocationMapSVG
                  class="w-full h-auto max-h-full"
                  businessName="Coffee Shop"
                  address="Main Street"
                  interactive={true}
                  showRoute={true}
                />
              </div>

              <!-- Appointment Demo -->
              <div id="appointment-demo" class="demo-content absolute inset-0 w-full h-full p-6 flex items-center justify-center hidden">
                <AppointmentSVG
                  class="w-full h-auto max-h-full"
                  doctorName="Dr. Smith"
                  appointmentDate="June 15"
                  appointmentTime="2:00 PM"
                  interactive={true}
                />
              </div>
            </div>
          </div>
          <!-- Dynamic Feature Highlights -->
          <div id="feature-highlight-1" class="absolute -right-4 top-1/4 md:-right-8 md:top-1/3 transform translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-xl border border-gray-200 dark:border-gray-700 hidden sm:block transition-all duration-300">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-2xl">🛍️</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Rich Commerce</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Interactive shopping</p>
              </div>
            </div>
          </div>

          <div id="feature-highlight-2" class="absolute -left-4 bottom-1/4 md:-left-8 md:bottom-1/3 transform -translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-xl border border-gray-200 dark:border-gray-700 hidden sm:block transition-all duration-300">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-accent-100 dark:bg-accent-900 flex items-center justify-center">
                <span class="text-2xl">⚡</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Real-time</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Instant responses</p>
              </div>
            </div>
          </div>

          <!-- Demo Progress Indicator -->
          <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <div id="progress-0" class="w-2 h-2 rounded-full bg-primary-600 transition-all duration-300"></div>
            <div id="progress-1" class="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 transition-all duration-300"></div>
            <div id="progress-2" class="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 transition-all duration-300"></div>
            <div id="progress-3" class="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 transition-all duration-300"></div>
            <div id="progress-4" class="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 transition-all duration-300"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  /* CSS Custom Properties for SVG theming */
  :root {
    --primary-500: #0ea5e9;
    --accent-500: #eab308;
  }

  .dark {
    --primary-500: #38bdf8;
    --accent-500: #fbbf24;
  }

  /* Ensure SVG adapts to theme */
  svg {
    color-scheme: light dark;
  }

  .dark svg rect[fill="#f8fafc"] {
    fill: #1f2937;
  }

  /* Demo transition styles */
  .demo-content {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  }

  .demo-content.hidden {
    opacity: 0;
    transform: scale(0.95);
  }

  .demo-content:not(.hidden) {
    opacity: 1;
    transform: scale(1);
  }
</style>

<script is:inline>
  // Interactive demo functionality
  document.addEventListener('DOMContentLoaded', function() {
    const demoButtons = {
      'demo-shopping': 'shopping-demo',
      'demo-ticket': 'ticket-demo',
      'demo-promo': 'promo-demo',
      'demo-map': 'map-demo',
      'demo-appointment': 'appointment-demo'
    };

    const buttons = Object.keys(demoButtons);
    const demos = Object.values(demoButtons);

    // Auto-cycle through demos
    let currentIndex = 0;
    let autoInterval;

    function showDemo(demoId, buttonId) {
      // Hide all demos
      demos.forEach(id => {
        const demo = document.getElementById(id);
        if (demo) demo.classList.add('hidden');
      });

      // Show selected demo
      const selectedDemo = document.getElementById(demoId);
      if (selectedDemo) selectedDemo.classList.remove('hidden');

      // Update button states
      buttons.forEach(id => {
        const btn = document.getElementById(id);
        if (btn) {
          if (id === buttonId) {
            btn.classList.add('bg-primary-600', 'text-white');
            btn.classList.remove('text-gray-600', 'dark:text-gray-400', 'hover:bg-primary-50', 'dark:hover:bg-primary-900');
          } else {
            btn.classList.remove('bg-primary-600', 'text-white');
            btn.classList.add('text-gray-600', 'dark:text-gray-400', 'hover:bg-primary-50', 'dark:hover:bg-primary-900');
          }
        }
      });

      // Update progress indicators
      const currentIndex = buttons.indexOf(buttonId);
      for (let i = 0; i < 5; i++) {
        const indicator = document.getElementById(`progress-${i}`);
        if (indicator) {
          if (i === currentIndex) {
            indicator.classList.add('bg-primary-600');
            indicator.classList.remove('bg-gray-300', 'dark:bg-gray-600');
          } else {
            indicator.classList.remove('bg-primary-600');
            indicator.classList.add('bg-gray-300', 'dark:bg-gray-600');
          }
        }
      }

      // Update feature highlights based on demo
      updateFeatureHighlights(buttonId);
    }

    function updateFeatureHighlights(buttonId) {
      const highlight1 = document.getElementById('feature-highlight-1');
      const highlight2 = document.getElementById('feature-highlight-2');

      const highlights = {
        'demo-shopping': {
          icon1: '🛍️', title1: 'Rich Commerce', desc1: 'Interactive shopping',
          icon2: '⚡', title2: 'Real-time', desc2: 'Instant responses'
        },
        'demo-ticket': {
          icon1: '🎫', title1: 'Digital Tickets', desc1: 'QR code scanning',
          icon2: '📱', title2: 'Mobile Wallet', desc2: 'One-tap saving'
        },
        'demo-promo': {
          icon1: '🔥', title1: 'Flash Sales', desc1: 'Limited time offers',
          icon2: '🎯', title2: 'Targeted', desc2: 'Personalized deals'
        },
        'demo-map': {
          icon1: '🗺️', title1: 'Navigation', desc1: 'Turn-by-turn',
          icon2: '📍', title2: 'Location', desc2: 'Real-time tracking'
        },
        'demo-appointment': {
          icon1: '📅', title1: 'Scheduling', desc1: 'Easy booking',
          icon2: '⏰', title2: 'Reminders', desc2: 'Never miss again'
        }
      };

      const config = highlights[buttonId];
      if (config && highlight1 && highlight2) {
        // Update highlight 1
        const icon1 = highlight1.querySelector('.text-2xl');
        const title1 = highlight1.querySelector('.font-medium');
        const desc1 = highlight1.querySelector('.text-sm');

        if (icon1) icon1.textContent = config.icon1;
        if (title1) title1.textContent = config.title1;
        if (desc1) desc1.textContent = config.desc1;

        // Update highlight 2
        const icon2 = highlight2.querySelector('.text-2xl');
        const title2 = highlight2.querySelector('.font-medium');
        const desc2 = highlight2.querySelector('.text-sm');

        if (icon2) icon2.textContent = config.icon2;
        if (title2) title2.textContent = config.title2;
        if (desc2) desc2.textContent = config.desc2;
      }
    }

    function startAutoDemo() {
      autoInterval = setInterval(() => {
        currentIndex = (currentIndex + 1) % buttons.length;
        const buttonId = buttons[currentIndex];
        const demoId = demoButtons[buttonId];
        showDemo(demoId, buttonId);
      }, 4000); // Change every 4 seconds
    }

    function stopAutoDemo() {
      if (autoInterval) {
        clearInterval(autoInterval);
        autoInterval = null;
      }
    }

    // Add click handlers
    buttons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', () => {
          stopAutoDemo();
          const demoId = demoButtons[buttonId];
          currentIndex = buttons.indexOf(buttonId);
          showDemo(demoId, buttonId);

          // Restart auto-demo after 10 seconds of inactivity
          setTimeout(() => {
            if (!autoInterval) {
              startAutoDemo();
            }
          }, 10000);
        });
      }
    });

    // Start auto-demo
    startAutoDemo();

    // Pause auto-demo when user hovers over the demo area
    const demoContainer = document.getElementById('demo-container');
    if (demoContainer) {
      demoContainer.addEventListener('mouseenter', stopAutoDemo);
      demoContainer.addEventListener('mouseleave', () => {
        setTimeout(() => {
          if (!autoInterval) {
            startAutoDemo();
          }
        }, 2000);
      });
    }
  });
</script>