<section class="py-20 bg-gray-50 dark:bg-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">How It Works</h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400">
        Simple integration process to get your RCS messaging up and running
      </p>
    </div>
    
    <!-- Steps -->
    <div class="relative">
      <!-- Connect line -->
      <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700 -ml-0.5"></div>
      
      <div class="space-y-16">
        <!-- Step 1 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex-1 md:pr-8 md:text-right order-2 md:order-1 mt-8 md:mt-0 reveal">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">Register and connect</h3>
            <p class="mt-3 text-gray-600 dark:text-gray-400">
              Create an account and connect to our RCS platform using our simple API keys and SDKs for multiple programming languages.
            </p>
            <a href="/docs/getting-started" class="mt-4 inline-flex items-center text-primary-600 dark:text-primary-400">
              View Documentation
              <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
          
          <div class="md:mx-auto flex items-center justify-center order-1 md:order-2">
            <div class="w-16 h-16 rounded-full border-4 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900 flex items-center justify-center z-10">
              <span class="text-xl font-bold text-primary-600 dark:text-primary-400">1</span>
            </div>
          </div>
          
          <div class="flex-1 md:pl-8 order-3 mt-8 md:mt-0">
            <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6 reveal">
              <pre is:raw class="text-sm text-gray-800 dark:text-gray-300 font-mono overflow-x-auto">
<code>// Initialize RCS client
const rcsClient = new RCSPlatform({
  apiKey: 'YOUR_API_KEY',
  projectId: 'YOUR_PROJECT_ID'
});

// Connect to service
await rcsClient.connect();</code>
              </pre>
            </div>
          </div>
        </div>
        
        <!-- Step 2 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex-1 md:pr-8 order-2 mt-8 md:mt-0 reveal">
            <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6">
              <pre is:raw class="text-sm text-gray-800 dark:text-gray-300 font-mono overflow-x-auto">
<code>// Create a rich message
const message = rcsClient.createMessage({
  recipient: '+15551234567',
  content: {
    title: 'Your order is confirmed!',
    text: 'Order #12345 has been processed',
    media: 'https://example.com/order.jpg',
    buttons: [
      {
        text: 'Track Order',
        url: 'https://example.com/track/12345'
      }
    ]
  }
});</code>
              </pre>
            </div>
          </div>
          
          <div class="md:mx-auto flex items-center justify-center order-1">
            <div class="w-16 h-16 rounded-full border-4 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900 flex items-center justify-center z-10">
              <span class="text-xl font-bold text-primary-600 dark:text-primary-400">2</span>
            </div>
          </div>
          
          <div class="flex-1 md:pl-8 md:text-left order-3 mt-8 md:mt-0 reveal">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">Create rich messages</h3>
            <p class="mt-3 text-gray-600 dark:text-gray-400">
              Build engaging messages with images, videos, buttons, and interactive elements using our simple API.
            </p>
            <a href="/docs/messaging" class="mt-4 inline-flex items-center text-primary-600 dark:text-primary-400">
              Message API Reference
              <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
        
        <!-- Step 3 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex-1 md:pr-8 md:text-right order-2 md:order-1 mt-8 md:mt-0 reveal">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">Send and track messages</h3>
            <p class="mt-3 text-gray-600 dark:text-gray-400">
              Send messages and track delivery, read receipts, and user interactions through our detailed analytics dashboard.
            </p>
            <a href="/docs/analytics" class="mt-4 inline-flex items-center text-primary-600 dark:text-primary-400">
              Analytics Documentation
              <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
          
          <div class="md:mx-auto flex items-center justify-center order-1 md:order-2">
            <div class="w-16 h-16 rounded-full border-4 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900 flex items-center justify-center z-10">
              <span class="text-xl font-bold text-primary-600 dark:text-primary-400">3</span>
            </div>
          </div>
          
          <div class="flex-1 md:pl-8 order-3 mt-8 md:mt-0">
            <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6 reveal">
              <pre is:raw class="text-sm text-gray-800 dark:text-gray-300 font-mono overflow-x-auto">
<code>// Send the message
const result = await rcsClient.send(message);

// Listen for events
rcsClient.on('delivered', (event) => {
  console.log(`Message delivered to ${event.recipient}`);
});

rcsClient.on('read', (event) => {
  console.log(`Message read by ${event.recipient}`);
});

rcsClient.on('buttonClicked', (event) => {
  console.log(`Button clicked: ${event.buttonId}`);
});</code>
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>