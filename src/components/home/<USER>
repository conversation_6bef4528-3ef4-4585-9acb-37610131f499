---
// No imports needed for direct SVG usage
---

<section class="relative overflow-hidden bg-gradient-to-b from-primary-50 to-white dark:from-gray-900 dark:to-gray-800 py-16 sm:py-24 md:py-32">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Text Column -->
      <div class="flex flex-col justify-center max-w-xl w-full mx-auto lg:mx-0">
        <h1 class="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
          Next-Generation Business Messaging
          <span class="text-primary-600 dark:text-primary-400">Platform</span>
        </h1>
        <p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
          Transform your customer engagement with rich, interactive messaging experiences. Reach customers where they are - in their default messaging app.
        </p>
        <div class="mt-8 flex items-center gap-x-6">
          <button
            id="heroJoinWaitingList"
            class="rounded-full bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-4 text-base font-semibold text-white shadow-lg hover:from-primary-700 hover:to-primary-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
          >
            Join Waiting List
          </button>
          <a href="/docs" class="text-base font-semibold leading-7 text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400">
            Documentation <span aria-hidden="true">→</span>
          </a>
        </div>
        <!-- Trust indicators -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Trusted by industry leaders</p>
          <div class="flex flex-wrap gap-8 items-center opacity-70">
            <div class="w-24 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <!-- More logos -->
          </div>
        </div>
      </div>
      <!-- Image Column -->
      <div class="relative flex items-center justify-center">
        <div class="relative w-full max-w-xs sm:max-w-md md:max-w-lg mx-auto" style="max-height:600px;">
          <div class="relative aspect-[9/16] rounded-3xl border-8 border-gray-900 dark:border-gray-700 shadow-2xl overflow-hidden bg-white dark:bg-gray-800">
            <!-- RCS Platform Preview SVG -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 640" class="absolute inset-0 w-full h-full object-cover">
              <defs>
                <linearGradient id="hero-grad" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:var(--primary-500, #0ea5e9);stop-opacity:0.1"/>
                  <stop offset="100%" style="stop-color:var(--primary-500, #0ea5e9);stop-opacity:0.3"/>
                </linearGradient>
                <linearGradient id="message-grad" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ffffff"/>
                  <stop offset="100%" style="stop-color:#f8fafc"/>
                </linearGradient>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                  <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
                  <feOffset dx="0" dy="2"/>
                  <feComponentTransfer>
                    <feFuncA type="linear" slope="0.2"/>
                  </feComponentTransfer>
                  <feMerge>
                    <feMergeNode/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>

              <!-- Background -->
              <rect width="360" height="640" fill="#f8fafc" class="dark:fill-gray-800"/>

              <!-- Header -->
              <rect x="0" y="0" width="360" height="80" fill="var(--primary-500, #0ea5e9)"/>
              <circle cx="40" cy="40" r="20" fill="#ffffff" opacity="0.9"/>
              <text x="80" y="45" fill="#ffffff" font-family="system-ui" font-size="18" font-weight="600">Business Chat</text>
              <text x="80" y="65" fill="#ffffff" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

              <!-- Welcome Message -->
              <g transform="translate(16, 96)">
                <rect width="280" height="70" rx="16" fill="url(#message-grad)" filter="url(#shadow)"/>
                <text x="20" y="30" font-family="system-ui" font-size="14" fill="#1f2937">Welcome to RCS Business Messaging! 👋</text>
                <text x="20" y="50" font-family="system-ui" font-size="14" fill="#64748b">How can we assist you today?</text>
              </g>

              <!-- Quick Reply Buttons -->
              <g transform="translate(16, 180)">
                <rect width="120" height="36" rx="18" fill="var(--primary-500, #0ea5e9)" filter="url(#shadow)"/>
                <text x="60" y="24" font-family="system-ui" font-size="14" fill="#ffffff" text-anchor="middle">Products 🛍️</text>
              </g>
              <g transform="translate(146, 180)">
                <rect width="120" height="36" rx="18" fill="var(--primary-500, #0ea5e9)" filter="url(#shadow)"/>
                <text x="60" y="24" font-family="system-ui" font-size="14" fill="#ffffff" text-anchor="middle">Support 💬</text>
              </g>

              <!-- Product Carousel -->
              <g transform="translate(16, 240)">
                <rect width="328" height="200" rx="16" fill="url(#hero-grad)" filter="url(#shadow)"/>

                <!-- Product Cards -->
                <g transform="translate(16, 16)">
                  <rect width="140" height="168" rx="12" fill="#ffffff"/>
                  <rect width="140" height="100" rx="8" fill="#e2e8f0"/>
                  <text x="70" y="140" font-family="system-ui" font-size="14" fill="#1f2937" text-anchor="middle">Product A</text>
                  <text x="70" y="160" font-family="system-ui" font-size="14" fill="var(--primary-500, #0ea5e9)" text-anchor="middle">$99.99</text>
                </g>

                <g transform="translate(172, 16)">
                  <rect width="140" height="168" rx="12" fill="#ffffff"/>
                  <rect width="140" height="100" rx="8" fill="#e2e8f0"/>
                  <text x="70" y="140" font-family="system-ui" font-size="14" fill="#1f2937" text-anchor="middle">Product B</text>
                  <text x="70" y="160" font-family="system-ui" font-size="14" fill="var(--primary-500, #0ea5e9)" text-anchor="middle">$149.99</text>
                </g>
              </g>

              <!-- Action Buttons -->
              <g transform="translate(16, 460)">
                <rect width="328" height="50" rx="25" fill="var(--accent-500, #eab308)" filter="url(#shadow)"/>
                <text x="164" y="32" font-family="system-ui" font-size="16" fill="#ffffff" text-anchor="middle" font-weight="600">Shop Now</text>
              </g>

              <!-- Typing Indicator -->
              <g transform="translate(16, 530)">
                <rect width="80" height="30" rx="15" fill="#e2e8f0" opacity="0.8"/>
                <circle cx="30" cy="15" r="3" fill="#64748b">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>
                </circle>
                <circle cx="40" cy="15" r="3" fill="#64748b">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
                </circle>
                <circle cx="50" cy="15" r="3" fill="#64748b">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
                </circle>
              </g>

              <!-- Bottom Navigation -->
              <g transform="translate(0, 580)">
                <rect width="360" height="60" fill="#ffffff" filter="url(#shadow)"/>
                <line x1="0" y1="0" x2="360" y2="0" stroke="#e2e8f0" stroke-width="1"/>

                <!-- Nav Items -->
                <g transform="translate(40, 20)">
                  <circle cx="0" cy="0" r="4" fill="var(--primary-500, #0ea5e9)"/>
                  <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Home</text>
                </g>
                <g transform="translate(140, 20)">
                  <circle cx="0" cy="0" r="4" fill="#64748b"/>
                  <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Cart</text>
                </g>
                <g transform="translate(240, 20)">
                  <circle cx="0" cy="0" r="4" fill="#64748b"/>
                  <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Profile</text>
                </g>
                <g transform="translate(320, 20)">
                  <circle cx="0" cy="0" r="4" fill="#64748b"/>
                  <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">More</text>
                </g>
              </g>
            </svg>
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
          <!-- Floating feature highlights -->
          <div class="absolute -right-4 top-1/4 md:-right-8 md:top-1/3 transform translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg hidden sm:block">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-2xl">📱</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Rich Media</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Images, videos & more</p>
              </div>
            </div>
          </div>
          <div class="absolute -left-4 bottom-1/4 md:-left-8 md:bottom-1/3 transform -translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg hidden sm:block">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-2xl">🔄</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Interactive</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Real-time engagement</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  /* CSS Custom Properties for SVG theming */
  :root {
    --primary-500: #0ea5e9;
    --accent-500: #eab308;
  }

  .dark {
    --primary-500: #38bdf8;
    --accent-500: #fbbf24;
  }

  /* Ensure SVG adapts to theme */
  svg {
    color-scheme: light dark;
  }

  .dark svg rect[fill="#f8fafc"] {
    fill: #1f2937;
  }
</style>