---
import { Image } from 'astro:assets';
---

<section id="use-cases" class="py-20 bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
        Transforming Industries with RCS
      </h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Discover how businesses across different sectors are leveraging our RCS platform to create exceptional customer experiences.
      </p>
    </div>

    <!-- Retail & E-commerce -->
    <div class="mb-24">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Retail & E-commerce</h3>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Order Tracking & Delivery Updates</h4>
          <p class="mb-6">Keep customers informed with real-time order status updates, including interactive maps for delivery tracking.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Order #12345 Update</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Your package is out for delivery</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <div class="h-32 bg-gray-200 dark:bg-gray-600 rounded-lg mb-3 flex items-center justify-center">
                <span class="text-gray-500 dark:text-gray-400">Interactive Delivery Map</span>
              </div>
              <p class="text-sm">Estimated delivery: Today, 2:00 PM - 4:00 PM</p>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Track Live</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Reschedule</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Real-time delivery tracking</li>
              <li>Delivery time estimates</li>
              <li>Rescheduling options</li>
              <li>Proof of delivery</li>
            </ul>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Interactive Receipts & Loyalty</h4>
          <p class="mb-6">Transform traditional receipts into interactive experiences that drive repeat purchases and loyalty.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Purchase Confirmation</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Thank you for shopping with us!</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Receipt Summary</h5>
              <div class="space-y-1 text-sm mb-3">
                <div class="flex justify-between">
                  <span>Organic Apples</span>
                  <span>$4.99</span>
                </div>
                <div class="flex justify-between">
                  <span>Whole Grain Bread</span>
                  <span>$3.49</span>
                </div>
                <div class="flex justify-between">
                  <span>Free Range Eggs</span>
                  <span>$5.99</span>
                </div>
                <div class="border-t pt-1 mt-1 font-medium flex justify-between">
                  <span>Total</span>
                  <span>$14.47</span>
                </div>
              </div>
              
              <div class="bg-green-50 dark:bg-green-900/30 p-2 rounded-lg text-sm text-green-800 dark:text-green-200 mb-3">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>You earned 29 loyalty points!</span>
                </div>
                <div class="mt-1">Total points: 450</div>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Order Again</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Leave Review</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Digital receipts with item details</li>
              <li>Loyalty points tracking</li>
              <li>One-click reordering</li>
              <li>Customer feedback collection</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Education -->
    <div class="mb-24">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Education</h3>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Parent-School Communication</h4>
          <p class="mb-6">Streamline communication between schools and parents with interactive messaging.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Westfield Elementary</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Term 2 Report Card Available</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Emma Johnson - Grade 4</h5>
              <div class="space-y-2 text-sm mb-3">
                <div class="flex justify-between">
                  <span>Mathematics</span>
                  <span class="font-medium text-green-600 dark:text-green-400">A-</span>
                </div>
                <div class="flex justify-between">
                  <span>Science</span>
                  <span class="font-medium text-green-600 dark:text-green-400">B+</span>
                </div>
                <div class="flex justify-between">
                  <span>English</span>
                  <span class="font-medium text-green-600 dark:text-green-400">A</span>
                </div>
                <div class="flex justify-between">
                  <span>History</span>
                  <span class="font-medium text-green-600 dark:text-green-400">B</span>
                </div>
              </div>
              
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                <p>Teacher's comment: Emma continues to show excellent progress in all subjects. She participates actively in class discussions.</p>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">View Full Report</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Schedule Meeting</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Digital report cards</li>
              <li>Fee balance inquiries</li>
              <li>Parent-teacher meeting scheduling</li>
              <li>Homework updates and reminders</li>
            </ul>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Student Support & Resources</h4>
          <p class="mb-6">Provide students with easy access to learning resources and support services.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Learning Resources</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Math Exam Preparation</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Study Materials</h5>
              <div class="space-y-3 text-sm">
                <div class="p-2 border border-gray-200 dark:border-gray-700 rounded flex justify-between items-center">
                  <span>Algebra Practice Problems</span>
                  <button class="text-primary-600 dark:text-primary-400">Download</button>
                </div>
                <div class="p-2 border border-gray-200 dark:border-gray-700 rounded flex justify-between items-center">
                  <span>Geometry Formula Sheet</span>
                  <button class="text-primary-600 dark:text-primary-400">Download</button>
                </div>
                <div class="p-2 border border-gray-200 dark:border-gray-700 rounded flex justify-between items-center">
                  <span>Video Tutorial: Quadratic Equations</span>
                  <button class="text-primary-600 dark:text-primary-400">Watch</button>
                </div>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Access All Materials</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Ask a Question</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Access to study materials and resources</li>
              <li>Interactive learning content</li>
              <li>Direct questions to teachers</li>
              <li>Exam preparation guidance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Healthcare -->
    <div class="mb-24">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Healthcare</h3>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Appointment Management</h4>
          <p class="mb-6">Streamline patient appointments with interactive scheduling and reminders.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal-600 dark:text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Appointment Reminder</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Dr. Sarah Chen - Tomorrow at 10:00 AM</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Appointment Details</h5>
              <div class="space-y-2 text-sm mb-3">
                <div class="flex justify-between">
                  <span>Doctor:</span>
                  <span>Dr. Sarah Chen (Cardiologist)</span>
                </div>
                <div class="flex justify-between">
                  <span>Date:</span>
                  <span>June 15, 2025</span>
                </div>
                <div class="flex justify-between">
                  <span>Time:</span>
                  <span>10:00 AM - 10:30 AM</span>
                </div>
                <div class="flex justify-between">
                  <span>Location:</span>
                  <span>Main Clinic, Room 305</span>
                </div>
              </div>
              
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                <p>Please arrive 15 minutes early to complete any necessary paperwork. Bring your insurance card and ID.</p>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Confirm</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Reschedule</button>
              <button class="px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg text-sm">Cancel</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Appointment scheduling and reminders</li>
              <li>Pre-visit instructions</li>
              <li>Digital check-in</li>
              <li>Follow-up care coordination</li>
            </ul>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Medication Management</h4>
          <p class="mb-6">Help patients stay on track with medication reminders and refill requests.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Medication Reminder</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Time to take your evening medication</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Your Medications</h5>
              <div class="space-y-3 text-sm">
                <div class="p-2 border border-gray-200 dark:border-gray-700 rounded flex justify-between items-center">
                  <div>
                    <span class="font-medium">Lisinopril 10mg</span>
                    <p class="text-xs text-gray-500 dark:text-gray-400">1 tablet with evening meal</p>
                  </div>
                  <button class="text-primary-600 dark:text-primary-400">Taken</button>
                </div>
                <div class="p-2 border border-gray-200 dark:border-gray-700 rounded flex justify-between items-center">
                  <div>
                    <span class="font-medium">Atorvastatin 20mg</span>
                    <p class="text-xs text-gray-500 dark:text-gray-400">1 tablet at bedtime</p>
                  </div>
                  <button class="text-primary-600 dark:text-primary-400">Taken</button>
                </div>
              </div>
              
              <div class="mt-3 text-sm">
                <p class="font-medium">Refills Needed Soon:</p>
                <p class="text-red-600 dark:text-red-400">Lisinopril - 5 days remaining</p>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Request Refill</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">Medication List</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Medication reminders</li>
              <li>Refill requests</li>
              <li>Medication adherence tracking</li>
              <li>Side effect reporting</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Hospitality & Travel -->
    <div class="mb-24">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Hospitality & Travel</h3>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Hotel Concierge Services</h4>
          <p class="mb-6">Enhance guest experiences with interactive concierge services via messaging.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-600 dark:text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Grand Horizon Hotel</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Welcome to your stay!</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">How can we help you today?</h5>
              <div class="grid grid-cols-2 gap-2 text-sm mb-3">
                <button class="p-2 border border-gray-200 dark:border-gray-700 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700">
                  Room Service
                </button>
                <button class="p-2 border border-gray-200 dark:border-gray-700 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700">
                  Housekeeping
                </button>
                <button class="p-2 border border-gray-200 dark:border-gray-700 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700">
                  Spa Booking
                </button>
                <button class="p-2 border border-gray-200 dark:border-gray-700 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700">
                  Local Activities
                </button>
              </div>
              
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                <p>Your room is ready for check-in. Use your digital key in this message to access your room.</p>
              </div>
              
              <div class="bg-amber-50 dark:bg-amber-900/30 p-2 rounded-lg text-sm text-amber-800 dark:text-amber-200">
                <p class="font-medium">Digital Room Key</p>
                <div class="mt-1 h-12 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                  <span>QR Code / Digital Key</span>
                </div>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Chat with Concierge</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">View Amenities</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Digital check-in and room keys</li>
              <li>Room service ordering</li>
              <li>Concierge requests</li>
              <li>Local recommendations</li>
            </ul>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
          <h4 class="text-xl font-semibold mb-4">Travel Itinerary Management</h4>
          <p class="mb-6">Keep travelers informed with real-time updates and interactive itineraries.</p>
          
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="font-medium">Flight Update</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">Your flight KQ002 is on time</p>
              </div>
            </div>
            
            <div class="mb-4 bg-white dark:bg-gray-800 rounded-lg p-3">
              <h5 class="font-medium mb-2">Your Travel Itinerary</h5>
              <div class="space-y-3 text-sm">
                <div class="border-l-4 border-blue-500 pl-2">
                  <p class="font-medium">Flight KQ002</p>
                  <p>Nairobi (NBO) to London (LHR)</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Departure: 10:45 PM • Gate B12</p>
                </div>
                <div class="border-l-4 border-green-500 pl-2">
                  <p class="font-medium">Hotel Check-in</p>
                  <p>The Savoy London</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">June 16, 2025 • 3:00 PM</p>
                </div>
                <div class="border-l-4 border-purple-500 pl-2">
                  <p class="font-medium">Dinner Reservation</p>
                  <p>Gordon Ramsay Restaurant</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">June 16, 2025 • 8:00 PM</p>
                </div>
              </div>
              
              <div class="mt-3 bg-blue-50 dark:bg-blue-900/30 p-2 rounded-lg text-sm text-blue-800 dark:text-blue-200">
                <p>Boarding starts in 45 minutes. Please proceed to Gate B12.</p>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="px-3 py-2 bg-primary-600 text-white rounded-lg text-sm">Check In</button>
              <button class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">View Boarding Pass</button>
            </div>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Key Features:</strong></p>
            <ul class="list-disc list-inside mt-2">
              <li>Real-time flight updates</li>
              <li>Interactive itineraries</li>
              <li>Digital boarding passes</li>
              <li>Travel recommendations</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-12">
      <a href="/docs/use-cases" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200">
        Explore More Use Cases
        <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </a>
    </div>
  </div>
</section>
