---
import { Image } from 'astro:assets';
---

<section class="py-20 bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
        Message Studio
      </h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Design and build rich interactive messages with our intuitive drag-and-drop message studio
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
      <!-- Message Builder Interface -->
      <div>
        <h3 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Visual Message Builder</h3>
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          Create professional RCS messages without coding. Drag and drop components, customize content, and preview your messages in real-time.
        </p>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <!-- Studio Header -->
          <div class="bg-gray-100 dark:bg-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <div class="flex items-center justify-between">
              <h4 class="font-semibold text-gray-900 dark:text-white">Message Studio</h4>
              <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm">Preview</button>
                <button class="px-3 py-1 bg-primary-600 text-white rounded text-sm">Publish</button>
              </div>
            </div>
          </div>
          
          <!-- Studio Content -->
          <div class="flex">
            <!-- Component Palette -->
            <div class="w-1/3 bg-gray-50 dark:bg-gray-800 p-4 border-r border-gray-200 dark:border-gray-600">
              <h5 class="font-medium text-gray-900 dark:text-white mb-4">Components</h5>
              <div class="space-y-3">
                <!-- Text Component -->
                <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Text</span>
                  </div>
                </div>
                
                <!-- Image Component -->
                <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Image</span>
                  </div>
                </div>
                
                <!-- Button Component -->
                <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Button</span>
                  </div>
                </div>
                
                <!-- Carousel Component -->
                <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Carousel</span>
                  </div>
                </div>
                
                <!-- Quick Reply Component -->
                <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Quick Reply</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Canvas Area -->
            <div class="flex-1 p-6">
              <h5 class="font-medium text-gray-900 dark:text-white mb-4">Message Canvas</h5>
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 min-h-[400px]">
                <!-- Sample Message Being Built -->
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-4 mb-4">
                  <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                    <div class="ml-2">
                      <p class="font-medium text-sm text-gray-900 dark:text-white">Your Store</p>
                    </div>
                  </div>
                  
                  <div class="mb-3">
                    <div class="w-full h-32 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                      <span class="text-gray-500 dark:text-gray-400 text-sm">Product Image</span>
                    </div>
                  </div>
                  
                  <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Summer Sale - 50% Off!</h4>
                  <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">Don't miss our biggest sale of the year. Shop now and save on all summer items.</p>
                  
                  <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm">Shop Now</button>
                    <button class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm">View Catalog</button>
                  </div>
                </div>
                
                <div class="text-center text-gray-500 dark:text-gray-400 text-sm">
                  Drag components here to build your message
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Studio Features:</strong></p>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Drag-and-drop message builder</li>
            <li>Real-time preview</li>
            <li>Pre-built templates</li>
            <li>Custom branding options</li>
            <li>Multi-device testing</li>
          </ul>
        </div>
      </div>
      
      <!-- Message Templates -->
      <div>
        <h3 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Ready-to-Use Templates</h3>
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          Start with professionally designed templates for common business scenarios. Customize them to match your brand and messaging needs.
        </p>
        
        <div class="space-y-6">
          <!-- Template 1: Promotional -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h4 class="font-semibold text-gray-900 dark:text-white">Promotional Campaign</h4>
                <button class="px-3 py-1 bg-primary-600 text-white rounded text-sm">Use Template</button>
              </div>
            </div>
            <div class="p-4">
              <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-4 text-white">
                <h5 class="font-bold mb-2">Flash Sale Alert! ⚡</h5>
                <p class="text-sm mb-3">24-hour flash sale - Up to 70% off selected items</p>
                <div class="flex space-x-2">
                  <button class="px-3 py-1 bg-white text-purple-600 rounded text-sm">Shop Now</button>
                  <button class="px-3 py-1 bg-purple-600 text-white rounded text-sm">View Deals</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Template 2: Appointment -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h4 class="font-semibold text-gray-900 dark:text-white">Appointment Reminder</h4>
                <button class="px-3 py-1 bg-primary-600 text-white rounded text-sm">Use Template</button>
              </div>
            </div>
            <div class="p-4">
              <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
                <div class="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h5 class="font-bold text-blue-900 dark:text-blue-100">Appointment Tomorrow</h5>
                </div>
                <p class="text-blue-800 dark:text-blue-200 text-sm mb-3">Dr. Smith - June 15, 2025 at 2:00 PM</p>
                <div class="flex space-x-2">
                  <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">Confirm</button>
                  <button class="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded text-sm">Reschedule</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Template 3: Order Update -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h4 class="font-semibold text-gray-900 dark:text-white">Order Update</h4>
                <button class="px-3 py-1 bg-primary-600 text-white rounded text-sm">Use Template</button>
              </div>
            </div>
            <div class="p-4">
              <div class="bg-green-50 dark:bg-green-900/30 rounded-lg p-4">
                <div class="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 dark:text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                  </svg>
                  <h5 class="font-bold text-green-900 dark:text-green-100">Order Shipped! 📦</h5>
                </div>
                <p class="text-green-800 dark:text-green-200 text-sm mb-3">Order #12345 is on its way. Expected delivery: June 16</p>
                <div class="flex space-x-2">
                  <button class="px-3 py-1 bg-green-600 text-white rounded text-sm">Track Package</button>
                  <button class="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded text-sm">View Order</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Template Categories:</strong></p>
          <ul class="list-disc list-inside mt-2 grid grid-cols-2 gap-2">
            <li>E-commerce & Retail</li>
            <li>Healthcare & Appointments</li>
            <li>Travel & Hospitality</li>
            <li>Financial Services</li>
            <li>Education & Training</li>
            <li>Customer Support</li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Call to Action -->
    <div class="text-center mt-16">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Ready to Start Building?</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
        Join thousands of businesses already using our Message Studio to create engaging RCS experiences.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <button id="studioJoinWaitingList" class="px-8 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-xl font-medium hover:from-primary-700 hover:to-primary-800 transition-all duration-200 transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
          Join Waiting List
        </button>
        <a href="/docs" class="px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200">
          View Documentation
        </a>
      </div>
    </div>
  </div>
</section>
