---
import { Image } from 'astro:assets';
import heroImage from '../../assets/rcs/hero-preview.svg';
---

<section class="relative overflow-hidden bg-gradient-to-b from-primary-50 to-white dark:from-gray-900 dark:to-gray-800 py-16 sm:py-24 md:py-32">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Text Column -->
      <div class="flex flex-col justify-center max-w-xl w-full mx-auto lg:mx-0">
        <h1 class="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
          Next-Generation Business Messaging
          <span class="text-primary-600 dark:text-primary-400">Platform</span>
        </h1>
        <p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
          Transform your customer engagement with rich, interactive messaging experiences. Reach customers where they are - in their default messaging app.
        </p>
        <div class="mt-8 flex items-center gap-x-6">
          <button
            id="heroJoinWaitingList"
            class="rounded-full bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-4 text-base font-semibold text-white shadow-lg hover:from-primary-700 hover:to-primary-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl"
          >
            Join Waiting List
          </button>
          <a href="/docs" class="text-base font-semibold leading-7 text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400">
            Documentation <span aria-hidden="true">→</span>
          </a>
        </div>
        <!-- Trust indicators -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Trusted by industry leaders</p>
          <div class="flex flex-wrap gap-8 items-center opacity-70">
            <div class="w-24 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <!-- More logos -->
          </div>
        </div>
      </div>
      <!-- Image Column -->
      <div class="relative flex items-center justify-center">
        <div class="relative w-full max-w-xs sm:max-w-md md:max-w-lg mx-auto" style="max-height:600px;">
          <div class="relative aspect-[9/16] rounded-3xl border-8 border-gray-900 dark:border-gray-700 shadow-2xl overflow-hidden bg-white dark:bg-gray-800">
            <Image
              src={heroImage}
              alt="RCS Platform Preview"
              class="absolute inset-0 w-full h-full object-cover"
              width={360}
              height={640}
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
          <!-- Floating feature highlights -->
          <div class="absolute -right-4 top-1/4 md:-right-8 md:top-1/3 transform translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg hidden sm:block">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-2xl">📱</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Rich Media</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Images, videos & more</p>
              </div>
            </div>
          </div>
          <div class="absolute -left-4 bottom-1/4 md:-left-8 md:bottom-1/3 transform -translate-x-1/2 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg hidden sm:block">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-2xl">🔄</span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">Interactive</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Real-time engagement</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
</style>