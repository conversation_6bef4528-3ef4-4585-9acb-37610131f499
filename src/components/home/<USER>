<!-- Features Schema -->
<script type="application/ld+json" is:inline>
{
  "@context": "https://schema.org",
  "@type": "ItemList",
  "name": "RCS Platform Features",
  "description": "Comprehensive list of RCS messaging platform features and capabilities",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Rich Media Support",
        "description": "Send images, videos, audio files, and documents with high-quality delivery"
      }
    },
    {
      "@type": "ListItem",
      "position": 2,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Interactive Buttons",
        "description": "Add clickable buttons for calls-to-action, quick replies, and navigation"
      }
    },
    {
      "@type": "ListItem",
      "position": 3,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Message Carousels",
        "description": "Create scrollable product catalogs and interactive content galleries"
      }
    },
    {
      "@type": "ListItem",
      "position": 4,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Real-time Analytics",
        "description": "Track delivery, read receipts, and engagement metrics in real-time"
      }
    },
    {
      "@type": "ListItem",
      "position": 5,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Enterprise Security",
        "description": "End-to-end encryption and enterprise-grade security compliance"
      }
    },
    {
      "@type": "ListItem",
      "position": 6,
      "item": {
        "@type": "SoftwareFeature",
        "name": "Global Reach",
        "description": "Worldwide carrier support with 99.9% delivery reliability"
      }
    }
  ]
}
</script>

<section id="features" class="py-20 bg-white dark:bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section header -->
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Powerful RCS Messaging Features</h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400">
        Everything you need to create rich, interactive messaging experiences
      </p>
    </div>
    
    <!-- Features grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
      <!-- Feature 1 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Rich Media Messaging</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Send images, videos, and interactive cards that engage customers more effectively than SMS.
        </p>
        <a href="/docs/features/rich-media" class="mt-6 inline-flex items-center text-primary-600 dark:text-primary-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 2 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600 dark:text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Interactive Buttons</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Add clickable buttons to your messages that trigger actions, open websites, or initiate calls.
        </p>
        <a href="/docs/features/interactive-buttons" class="mt-6 inline-flex items-center text-secondary-600 dark:text-secondary-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 3 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Advanced Analytics</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Track message delivery, read receipts, and button interactions with detailed analytics.
        </p>
        <a href="/docs/features/analytics" class="mt-6 inline-flex items-center text-accent-600 dark:text-accent-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 4 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Transactional Messaging</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Send order confirmations, shipping updates, and payment receipts through rich RCS messages.
        </p>
        <a href="/docs/features/transactional" class="mt-6 inline-flex items-center text-primary-600 dark:text-primary-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 5 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600 dark:text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Chatbot Integration</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          Create conversational experiences with our AI-powered chatbot platform and RCS messaging.
        </p>
        <a href="/docs/features/chatbots" class="mt-6 inline-flex items-center text-secondary-600 dark:text-secondary-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <!-- Feature 6 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 transition-all hover:shadow-lg reveal">
        <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Enterprise Security</h3>
        <p class="mt-4 text-gray-600 dark:text-gray-400">
          End-to-end encryption, compliance features, and enterprise-grade security for all messages.
        </p>
        <a href="/docs/features/security" class="mt-6 inline-flex items-center text-accent-600 dark:text-accent-400">
          Learn more
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>