---
// UseCases.astro - Showcase different RCS applications with interactive demos
---

<section class="py-20 bg-gray-50 dark:bg-gray-800">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Transform Your Business Communication</h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400">
        See how RCS can revolutionize your customer interactions across different industries
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
      <!-- E-commerce Demo -->
      <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg reveal">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h3 class="ml-4 text-xl font-bold text-gray-900 dark:text-white">E-commerce</h3>
              </div>
        
        <div class="mb-6">
          <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4">
            <p class="text-gray-600 dark:text-gray-400 mb-4">Order #12345 Confirmed!</p>
            <img 
              src="https://images.pexels.com/photos/5632402/pexels-photo-5632402.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
              alt="Product" 
              class="w-full h-48 object-cover rounded-lg mb-4"
            />
            <div class="flex gap-2">
              <button class="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                Track Order
              </button>
              <button class="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                View Details
              </button>
            </div>
          </div>
        </div>
        
        <p class="text-gray-600 dark:text-gray-400">
          Send rich order confirmations, shipping updates, and enable direct purchases through RCS messages.
        </p>
      </div>

      <!-- Customer Service Demo -->
      <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg reveal">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600 dark:text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </div>
          <h3 class="ml-4 text-xl font-bold text-gray-900 dark:text-white">Customer Service</h3>
          </div>

        <div class="space-y-4 mb-6">
          <div class="flex items-start gap-4">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            </div>
            <div class="flex-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
              <p class="text-gray-800 dark:text-gray-200">Hi! How can I help you today?</p>
            </div>
          </div>

          <div class="flex items-start gap-4 justify-end">
            <div class="flex-1 bg-primary-600 rounded-lg p-4">
              <p class="text-white">I need help with my recent order</p>
            </div>
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-primary-200 rounded-full"></div>
            </div>
          </div>

          <div class="flex gap-2">
            <button class="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
              Check Order Status
            </button>
            <button class="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
              Contact Support
            </button>
          </div>
        </div>
        
        <p class="text-gray-600 dark:text-gray-400">
          Provide instant support with rich media, suggested replies, and seamless agent handoff.
        </p>
      </div>
    </div>

    <!-- Additional Use Cases -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Appointment Scheduling -->
      <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg reveal">
        <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Appointment Scheduling</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Send interactive appointment reminders with options to confirm, reschedule, or cancel.
        </p>
      </div>

      <!-- Marketing Campaigns -->
      <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg reveal">
        <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
          </svg>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Marketing Campaigns</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Create engaging promotional messages with rich media and interactive elements.
        </p>
      </div>

      <!-- Surveys & Feedback -->
      <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg reveal">
        <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Surveys & Feedback</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Collect customer feedback through interactive surveys and rating systems.
        </p>
      </div>
    </div>
  </div>
</section>