---
import { Image } from 'astro:assets';
---

<section id="use-cases" class="py-20 bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
        Rich Interactive Messaging
      </h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Create engaging, interactive experiences that drive customer action and satisfaction
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <!-- Digital Ticket Example -->
      <div>
        <h3 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Digital Tickets & Passes</h3>
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          Send interactive, scannable tickets and passes directly to your customers' messaging app. No more paper tickets or separate apps needed.
        </p>
        
        <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden">
          <!-- SVG Ticket -->
          <svg class="w-full" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="400" height="220" rx="12" fill="url(#ticket-gradient)" />
            
            <!-- Top Section -->
            <rect x="20" y="20" width="360" height="100" rx="8" fill="white" fill-opacity="0.9" />
            
            <!-- Event Details -->
            <text x="40" y="45" font-family="system-ui" font-weight="bold" font-size="16" fill="#111827">SUMMER MUSIC FESTIVAL</text>
            <text x="40" y="70" font-family="system-ui" font-size="14" fill="#4B5563">July 15-17, 2025 • Central Park</text>
            <text x="40" y="95" font-family="system-ui" font-size="14" fill="#4B5563">3-Day Pass • General Admission</text>
            
            <!-- QR Code Placeholder -->
            <rect x="280" y="30" width="80" height="80" rx="4" fill="#E5E7EB" />
            <text x="320" y="75" font-family="system-ui" font-size="10" fill="#6B7280" text-anchor="middle">SCAN ME</text>
            
            <!-- Ticket Details -->
            <line x1="20" y1="140" x2="380" y2="140" stroke="#E5E7EB" stroke-width="2" stroke-dasharray="6 4" />
            
            <text x="40" y="165" font-family="system-ui" font-weight="bold" font-size="14" fill="white">TICKET #: VIP-2025-78945</text>
            <text x="40" y="190" font-family="system-ui" font-size="14" fill="white">Sarah Johnson</text>
            
            <text x="340" y="165" font-family="system-ui" font-weight="bold" font-size="14" fill="white" text-anchor="end">GATE 4</text>
            <text x="340" y="190" font-family="system-ui" font-size="14" fill="white" text-anchor="end">SECTION A</text>
            
            <!-- Gradient Definition -->
            <defs>
              <linearGradient id="ticket-gradient" x1="0" y1="0" x2="400" y2="220" gradientUnits="userSpaceOnUse">
                <stop offset="0%" stop-color="#4F46E5" />
                <stop offset="100%" stop-color="#7C3AED" />
              </linearGradient>
            </defs>
          </svg>
          
          <!-- Action Buttons -->
          <div class="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between">
            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm">Add to Wallet</button>
            <button class="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm">Share Ticket</button>
          </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Key Features:</strong></p>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Scannable QR codes for easy entry</li>
            <li>Add to digital wallet with one tap</li>
            <li>Real-time updates for gate changes or delays</li>
            <li>Location directions to venue</li>
          </ul>
        </div>
      </div>
      
      <!-- Navigation Example -->
      <div>
        <h3 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Interactive Navigation</h3>
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          Help customers find your business with interactive maps and directions, all within their messaging app.
        </p>
        
        <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden">
          <!-- Conversation -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-start mb-4">
              <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 max-w-[80%]">
                <p class="text-gray-800 dark:text-gray-200">I'm on my way to your store. Where are you located?</p>
              </div>
            </div>
            
            <div class="flex items-start justify-end mb-4">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-lg p-3 max-w-[80%]">
                <p class="text-primary-800 dark:text-primary-200">We're at 123 Main Street. Here's a map to help you find us:</p>
              </div>
            </div>
            
            <!-- SVG Map -->
            <div class="flex justify-end mb-4">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-lg p-3 max-w-[80%]">
                <svg class="w-full h-48" viewBox="0 0 300 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="300" height="200" rx="8" fill="#E5E7EB" />
                  
                  <!-- Map Elements -->
                  <rect x="20" y="30" width="260" height="140" fill="#D1FAE5" />
                  
                  <!-- Roads -->
                  <rect x="50" y="30" width="10" height="140" fill="#9CA3AF" />
                  <rect x="20" y="80" width="260" height="10" fill="#9CA3AF" />
                  <rect x="150" y="30" width="10" height="140" fill="#9CA3AF" />
                  <rect x="20" y="140" width="260" height="10" fill="#9CA3AF" />
                  
                  <!-- Buildings -->
                  <rect x="70" y="40" width="30" height="30" fill="#E5E7EB" stroke="#9CA3AF" />
                  <rect x="170" y="40" width="40" height="30" fill="#E5E7EB" stroke="#9CA3AF" />
                  <rect x="70" y="100" width="30" height="30" fill="#E5E7EB" stroke="#9CA3AF" />
                  
                  <!-- Store Location -->
                  <rect x="170" y="100" width="40" height="30" fill="#EF4444" stroke="#9CA3AF" />
                  <circle cx="190" cy="115" r="10" fill="#FEF2F2" />
                  <path d="M190 110 L190 120 M185 115 L195 115" stroke="#EF4444" stroke-width="2" />
                  
                  <!-- Current Location -->
                  <circle cx="80" y="55" r="8" fill="#3B82F6" />
                  <circle cx="80" y="55" r="12" fill="#3B82F6" fill-opacity="0.3" />
                  
                  <!-- Route -->
                  <path d="M80 55 L80 85 L190 85 L190 115" stroke="#3B82F6" stroke-width="3" stroke-dasharray="5 3" />
                  
                  <!-- Labels -->
                  <text x="20" y="20" font-family="system-ui" font-size="12" fill="#111827">Your Location</text>
                  <text x="230" y="20" font-family="system-ui" font-size="12" fill="#111827" text-anchor="end">Our Store</text>
                  <text x="190" y="180" font-family="system-ui" font-size="12" fill="#111827" text-anchor="middle">123 Main Street</text>
                </svg>
                
                <div class="mt-2 text-primary-800 dark:text-primary-200 text-sm">
                  <p>We're open until 8:00 PM today.</p>
                </div>
              </div>
            </div>
            
            <div class="flex items-start mb-4">
              <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 max-w-[80%]">
                <p class="text-gray-800 dark:text-gray-200">Thanks! How long will it take me to get there?</p>
              </div>
            </div>
            
            <div class="flex items-start justify-end">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-lg p-3 max-w-[80%]">
                <p class="text-primary-800 dark:text-primary-200">Based on current traffic, it should take about 15 minutes. Would you like turn-by-turn directions?</p>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="p-4 bg-gray-50 dark:bg-gray-800 flex flex-wrap gap-2">
            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm">Get Directions</button>
            <button class="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm">Call Store</button>
            <button class="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm">Share Location</button>
          </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Key Features:</strong></p>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Interactive maps with real-time location</li>
            <li>Turn-by-turn directions</li>
            <li>Business hours and information</li>
            <li>One-tap navigation integration</li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Mini Catalog Example -->
    <div class="mt-20">
      <h3 class="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">Interactive Product Catalogs</h3>
      <p class="mb-12 text-center text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Showcase your products with rich, interactive catalogs that let customers browse and purchase without leaving their messaging app.
      </p>
      
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden max-w-4xl mx-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="font-medium text-gray-900 dark:text-white">Fashion Boutique</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">Summer Collection 2025</p>
            </div>
          </div>
          
          <p class="text-gray-800 dark:text-gray-200 mb-4">
            Check out our new summer arrivals! Browse the catalog below and tap any item to see details.
          </p>
          
          <!-- Product Catalog Grid -->
          <div class="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-6">
            <!-- Product 1 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 16 L44 16 L42 44 L22 44 Z" fill="#FBBF24" />
                  <path d="M25 16 L25 12 C25 8 29 4 32 4 C35 4 39 8 39 12 L39 16" stroke="#78350F" stroke-width="2" fill="none" />
                  <path d="M20 16 L44 16 L42 44 L22 44 Z" stroke="#78350F" stroke-width="2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Summer Tote</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$49.99</p>
            </div>
            
            <!-- Product 2 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 24 L48 24 L48 48 C48 52 44 56 40 56 L24 56 C20 56 16 52 16 48 Z" fill="#93C5FD" />
                  <path d="M24 24 L24 16 C24 10 28 8 32 8 C36 8 40 10 40 16 L40 24" stroke="#1E40AF" stroke-width="2" fill="none" />
                  <path d="M16 24 L48 24 L48 48 C48 52 44 56 40 56 L24 56 C20 56 16 52 16 48 Z" stroke="#1E40AF" stroke-width="2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Denim Jacket</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$89.99</p>
            </div>
            
            <!-- Product 3 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 12 L44 12 L44 52 L32 60 L20 52 Z" fill="#A7F3D0" />
                  <path d="M20 12 L44 12 L44 52 L32 60 L20 52 Z" stroke="#065F46" stroke-width="2" fill="none" />
                  <path d="M32 12 L32 60" stroke="#065F46" stroke-width="2" stroke-dasharray="2 2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Summer Dress</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$65.99</p>
            </div>
            
            <!-- Product 4 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="24" fill="#FEE2E2" />
                  <path d="M32 16 C24 16 16 22 16 32 C16 42 24 48 32 48 C40 48 48 42 48 32 C48 22 40 16 32 16 Z" stroke="#991B1B" stroke-width="2" fill="none" />
                  <path d="M32 16 L32 48" stroke="#991B1B" stroke-width="2" fill="none" />
                  <path d="M16 32 L48 32" stroke="#991B1B" stroke-width="2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Sun Hat</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$29.99</p>
            </div>
            
            <!-- Product 5 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 24 L48 24 L44 56 L20 56 Z" fill="#C7D2FE" />
                  <path d="M24 24 L28 12 L36 12 L40 24" stroke="#3730A3" stroke-width="2" fill="none" />
                  <path d="M16 24 L48 24 L44 56 L20 56 Z" stroke="#3730A3" stroke-width="2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Beach Shorts</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$35.99</p>
            </div>
            
            <!-- Product 6 -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
              <div class="aspect-square bg-white dark:bg-gray-700 rounded-md mb-2 flex items-center justify-center">
                <svg class="w-16 h-16" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="12" y="12" width="40" height="40" rx="4" fill="#DBEAFE" />
                  <path d="M20 20 L44 20 M20 32 L44 32 M20 44 L44 44" stroke="#1E3A8A" stroke-width="2" fill="none" />
                  <path d="M32 12 L32 52" stroke="#1E3A8A" stroke-width="2" fill="none" />
                </svg>
              </div>
              <p class="font-medium text-sm text-gray-900 dark:text-white">Plaid Shirt</p>
              <p class="text-sm text-primary-600 dark:text-primary-400">$45.99</p>
            </div>
          </div>
          
          <!-- Product Detail View -->
          <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="w-full sm:w-1/3 aspect-square bg-white dark:bg-gray-700 rounded-md flex items-center justify-center">
                <svg class="w-24 h-24" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M24 36 L72 36 L72 84 C72 88 68 92 64 92 L32 92 C28 92 24 88 24 84 Z" fill="#93C5FD" />
                  <path d="M36 36 L36 24 C36 16 42 12 48 12 C54 12 60 16 60 24 L60 36" stroke="#1E40AF" stroke-width="3" fill="none" />
                  <path d="M24 36 L72 36 L72 84 C72 88 68 92 64 92 L32 92 C28 92 24 88 24 84 Z" stroke="#1E40AF" stroke-width="3" fill="none" />
                  <circle cx="36" cy="54" r="4" fill="#1E40AF" />
                  <circle cx="60" cy="54" r="4" fill="#1E40AF" />
                  <path d="M36 72 C36 66 42 62 48 62 C54 62 60 66 60 72" stroke="#1E40AF" stroke-width="2" fill="none" />
                </svg>
              </div>
              
              <div class="flex-1">
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="font-bold text-gray-900 dark:text-white">Denim Jacket</h4>
                    <p class="text-lg text-primary-600 dark:text-primary-400 font-medium">$89.99</p>
                  </div>
                  <div class="flex space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300 dark:text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300 dark:text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>
                
                <p class="mt-2 text-gray-600 dark:text-gray-400 text-sm">
                  Classic denim jacket with a modern fit. Perfect for cool summer evenings.
                </p>
                
                <div class="mt-4">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Size</p>
                  <div class="flex space-x-2 mt-2">
                    <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs">XS</button>
                    <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs">S</button>
                    <button class="w-8 h-8 rounded-full bg-primary-600 text-white flex items-center justify-center text-xs">M</button>
                    <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs">L</button>
                    <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs">XL</button>
                  </div>
                </div>
                
                <div class="mt-4">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Color</p>
                  <div class="flex space-x-2 mt-2">
                    <button class="w-8 h-8 rounded-full bg-blue-500 border-2 border-white dark:border-gray-800 shadow-sm"></button>
                    <button class="w-8 h-8 rounded-full bg-gray-800 border-2 border-white dark:border-gray-800 shadow-sm"></button>
                    <button class="w-8 h-8 rounded-full bg-indigo-700 border-2 border-white dark:border-gray-800 shadow-sm"></button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between">
          <div class="flex items-center">
            <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
              </svg>
            </button>
            <span class="mx-3 font-medium">1</span>
            <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
          <button class="px-6 py-2 bg-primary-600 text-white rounded-lg text-sm">Add to Cart</button>
        </div>
      </div>
      
      <div class="mt-6 text-sm text-gray-600 dark:text-gray-400 max-w-4xl mx-auto">
        <p><strong>Key Features:</strong></p>
        <ul class="list-disc list-inside mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
          <li>Interactive product browsing</li>
          <li>Rich product details with images</li>
          <li>Size and color selection</li>
          <li>Customer reviews and ratings</li>
          <li>One-tap add to cart</li>
          <li>Personalized recommendations</li>
          <li>Secure checkout process</li>
          <li>Order tracking integration</li>
        </ul>
      </div>
    </div>
    
    <!-- Restaurant Menu Example -->
    <div class="mt-20">
      <h3 class="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">Interactive Restaurant Menus</h3>
      <p class="mb-12 text-center text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        Allow customers to browse your menu, place orders, and make reservations directly through messaging.
      </p>
      
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden max-w-4xl mx-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="font-medium text-gray-900 dark:text-white">Bella Italia Restaurant</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">Authentic Italian Cuisine</p>
            </div>
          </div>
          
          <p class="text-gray-800 dark:text-gray-200 mb-4">
            Browse our menu and place your order directly through this message. We'll have it ready for pickup or delivery!
          </p>
          
          <!-- Menu Categories -->
          <div class="flex overflow-x-auto space-x-2 pb-2 mb-4">
            <button class="px-4 py-2 bg-primary-600 text-white rounded-full text-sm whitespace-nowrap">All Items</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm whitespace-nowrap">Appetizers</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm whitespace-nowrap">Pasta</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm whitespace-nowrap">Pizza</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm whitespace-nowrap">Desserts</button>
            <button class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm whitespace-nowrap">Drinks</button>
          </div>
          
          <!-- Menu Items -->
          <div class="space-y-4 mb-6">
            <!-- Menu Item 1 -->
            <div class="flex border-b border-gray-200 dark:border-gray-700 pb-4">
              <div class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                <svg class="w-12 h-12" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="20" fill="#FECACA" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" fill="#FBBF24" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" stroke="#78350F" stroke-width="2" fill="none" />
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-bold text-gray-900 dark:text-white">Margherita Pizza</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Tomato sauce, mozzarella, fresh basil</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$12.99</p>
              </div>
              <div class="w-16 flex items-center justify-center">
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                <span class="mx-3 font-medium">1</span>
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- Menu Item 2 -->
            <div class="flex border-b border-gray-200 dark:border-gray-700 pb-4">
              <div class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                <svg class="w-12 h-12" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="20" fill="#FECACA" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" fill="#FBBF24" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" stroke="#78350F" stroke-width="2" fill="none" />
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-bold text-gray-900 dark:text-white">Pepperoni Pizza</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Tomato sauce, mozzarella, pepperoni</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$13.99</p>
              </div>
              <div class="w-16 flex items-center justify-center">
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                <span class="mx-3 font-medium">1</span>
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- Menu Item 3 -->
            <div class="flex border-b border-gray-200 dark:border-gray-700 pb-4">
              <div class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                <svg class="w-12 h-12" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="20" fill="#FECACA" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" fill="#FBBF24" />
                  <path d="M14 20 C14 14 24 10 24 10 C34 10 44 14 44 20 C44 26 34 30 24 30 C14 30 4 26 4 20 C4 14 14 10 24 10 Z" stroke="#78350F" stroke-width="2" fill="none" />
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-bold text-gray-900 dark:text-white">Caesar Salad</h4>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Romaine lettuce, croutons, parmesan, Caesar dressing</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$8.99</p>
              </div>
              <div class="w-16 flex items-center justify-center">
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                <span class="mx-3 font-medium">1</span>
                <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Order Summary -->
          <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
            <div class="flex justify-between items-center mb-4">
              <h4 class="font-bold text-gray-900 dark:text-white">Order Summary</h4>
              <button class="text-primary-600 dark:text-primary-400 font-medium">Edit Order</button>
            </div>
            
            <div class="space-y-2">
              <div class="flex justify-between">
                <p class="text-gray-600 dark:text-gray-400">Subtotal</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$35.87</p>
              </div>
              <div class="flex justify-between">
                <p class="text-gray-600 dark:text-gray-400">Tax</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$3.59</p>
              </div>
              <div class="flex justify-between">
                <p class="text-gray-600 dark:text-gray-400">Delivery Fee</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$3.00</p>
              </div>
              <div class="flex justify-between">
                <p class="text-gray-600 dark:text-gray-400">Total</p>
                <p class="text-gray-800 dark:text-gray-200 font-medium">$42.46</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between">
          <div class="flex items-center">
            <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
              </svg>
            </button>
            <span class="mx-3 font-medium">1</span>
            <button class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
          <button class="px-6 py-2 bg-primary-600 text-white rounded-lg text-sm">Place Order</button>
        </div>
      </div>
      
      <div class="mt-6 text-sm text-gray-600 dark:text-gray-400 max-w-4xl mx-auto">
        <p><strong>Key Features:</strong></p>
        <ul class="list-disc list-inside mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
          <li>Interactive menu browsing</li>
          <li>Order customization</li>
          <li>Real-time order tracking</li>
          <li>Payment integration</li>
          <li>Delivery and pickup options</li>
          <li>Reservation management</li>
          <li>Customer feedback collection</li>
        </ul>
      </div>
    </div>
  </div>
</section>
