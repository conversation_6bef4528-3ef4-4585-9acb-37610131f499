---
// TechnicalOverview.astro - Technical capabilities and integration examples
---

<section class="py-20 bg-white dark:bg-gray-900">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Technical Capabilities</h2>
      <p class="mt-4 text-xl text-gray-600 dark:text-gray-400">
        Powerful features and easy integration for developers
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
      <!-- Code Example -->
      <div class="bg-gray-900 rounded-xl p-6 shadow-xl reveal">
        <div class="flex items-center mb-4">
          <div class="flex space-x-2">
            <div class="w-3 h-3 rounded-full bg-red-500"></div>
            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div class="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
        </div>
        <pre is:raw class="text-sm text-gray-300 font-mono overflow-x-auto">
<code>// Initialize the RCS client
const client = new RCSPlatform({
  apiKey: 'your-api-key'
});

// Create a rich card message
const message = await client.createMessage({
  recipient: '+1234567890',
  type: 'rich_card',
  content: {
    title: 'New Product Launch',
    description: 'Check out our latest product!',
    media: {
      type: 'image',
      url: 'https://example.com/product.jpg'
    },
    suggestions: [
      {
        type: 'action',
        text: 'Buy Now',
        postback: '/buy'
      },
      {
        type: 'url',
        text: 'Learn More',
        url: 'https://example.com/product'
      }
    ]
  }
});

// Send the message
const result = await client.send(message);

// Handle delivery status
client.on('delivered', (event) => {
  console.log(`Message delivered to ${event.recipient}`);
});</code>
        </pre>
      </div>

      <!-- Features List -->
      <div class="space-y-8">
        <div class="flex items-start reveal">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Simple API Integration</h3>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              Easy-to-use SDK with comprehensive documentation and examples for quick implementation.
            </p>
          </div>
        </div>

        <div class="flex items-start reveal">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600 dark:text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Enterprise Security</h3>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              End-to-end encryption, compliance features, and enterprise-grade security standards.
            </p>
          </div>
        </div>

        <div class="flex items-start reveal">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-accent-100 dark:bg-accent-900 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600 dark:text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Advanced Analytics</h3>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              Detailed insights into message delivery, engagement, and user interactions.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Integration Steps -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="text-center reveal">
        <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-6">
          <span class="text-2xl font-bold text-primary-600 dark:text-primary-400">1</span>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Sign Up</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Create an account and get your API credentials
        </p>
      </div>

      <div class="text-center reveal">
        <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center mx-auto mb-6">
          <span class="text-2xl font-bold text-secondary-600 dark:text-secondary-400">2</span>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Integrate</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Add our SDK to your application with just a few lines of code
        </p>
      </div>

      <div class="text-center reveal">
        <div class="w-16 h-16 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center mx-auto mb-6">
          <span class="text-2xl font-bold text-accent-600 dark:text-accent-400">3</span>
        </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Launch</h3>
        <p class="text-gray-600 dark:text-gray-400">
          Start sending rich, interactive messages to your customers
        </p>
      </div>
    </div>
  </div>
</section>