<aside class="docs-sidebar w-64 fixed top-24 bottom-0 left-0 hidden md:block overflow-y-auto py-6 px-4 border-r border-gray-200 dark:border-gray-800">
  <nav class="space-y-1">
    <div class="mb-6">
      <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">Getting Started</h3>
      <ul class="space-y-2">
        <li><a href="/docs" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Introduction</a></li>
        <li><a href="/docs/quickstart" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Quick Start</a></li>
        <li><a href="/docs/installation" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Installation</a></li>
      </ul>
    </div>
    
    <div class="mb-6">
      <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">Core Concepts</h3>
      <ul class="space-y-2">
        <li><a href="/docs/core/overview" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">RCS Overview</a></li>
        <li><a href="/docs/core/messages" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Messages</a></li>
        <li><a href="/docs/core/interactive" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Interactive Elements</a></li>
        <li><a href="/docs/core/media" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Rich Media</a></li>
        <li><a href="/docs/core/events" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Events & Webhooks</a></li>
      </ul>
    </div>
    
    <div class="mb-6">
      <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">API Reference</h3>
      <ul class="space-y-2">
        <li><a href="/docs/api/authentication" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Authentication</a></li>
        <li><a href="/docs/api/messages" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Messages API</a></li>
        <li><a href="/docs/api/templates" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Templates API</a></li>
        <li><a href="/docs/api/analytics" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Analytics API</a></li>
        <li><a href="/docs/api/webhooks" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Webhooks</a></li>
      </ul>
    </div>
    
    <div class="mb-6">
      <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">Advanced</h3>
      <ul class="space-y-2">
        <li><a href="/docs/advanced/security" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Security</a></li>
        <li><a href="/docs/advanced/rate-limits" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Rate Limits</a></li>
        <li><a href="/docs/advanced/compliance" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Compliance</a></li>
        <li><a href="/docs/advanced/troubleshooting" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Troubleshooting</a></li>
      </ul>
    </div>
  </nav>
</aside>

<!-- Mobile sidebar toggle -->
<div class="md:hidden sticky top-16 z-10 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 p-4">
  <button 
    id="sidebar-toggle"
    class="flex items-center text-gray-600 dark:text-gray-400"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
    </svg>
    Menu
  </button>
  
  <!-- Mobile sidebar -->
  <div 
    id="mobile-sidebar"
    class="fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-50 hidden"
  >
    <div class="absolute right-0 top-0 bottom-0 w-64 bg-white dark:bg-gray-900 shadow-xl">
      <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-800">
        <h2 class="font-bold text-gray-900 dark:text-white">Documentation</h2>
        <button id="close-sidebar" class="text-gray-500 dark:text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="p-4 overflow-y-auto max-h-[calc(100vh-64px)]">
        <nav class="space-y-1">
          <div class="mb-6">
            <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">Getting Started</h3>
            <ul class="space-y-2">
              <li><a href="/docs" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Introduction</a></li>
              <li><a href="/docs/quickstart" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Quick Start</a></li>
              <li><a href="/docs/installation" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Installation</a></li>
            </ul>
          </div>
          
          <!-- Other categories from above -->
          <div class="mb-6">
            <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-3">Core Concepts</h3>
            <ul class="space-y-2">
              <li><a href="/docs/core/overview" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">RCS Overview</a></li>
              <li><a href="/docs/core/messages" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Messages</a></li>
              <li><a href="/docs/core/interactive" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Interactive Elements</a></li>
              <li><a href="/docs/core/media" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Rich Media</a></li>
              <li><a href="/docs/core/events" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 block">Events & Webhooks</a></li>
            </ul>
          </div>
          
          <!-- More categories as needed -->
        </nav>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const toggleButton = document.getElementById('sidebar-toggle');
    const closeButton = document.getElementById('close-sidebar');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    
    if (toggleButton && closeButton && mobileSidebar) {
      toggleButton.addEventListener('click', () => {
        mobileSidebar.classList.remove('hidden');
      });
      
      closeButton.addEventListener('click', () => {
        mobileSidebar.classList.add('hidden');
      });
      
      // Close when clicking outside
      mobileSidebar.addEventListener('click', (e) => {
        if (e.target === mobileSidebar) {
          mobileSidebar.classList.add('hidden');
        }
      });
    }
  });
</script>