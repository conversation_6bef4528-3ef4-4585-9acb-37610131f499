---
export interface Props {
  toc: any[];
}

const { toc } = Astro.props;
---

<div class="table-of-contents">
  <h3 class="text-sm font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider mb-4">On this page</h3>
  <nav class="space-y-1 text-sm">
    {toc.map((item) => (
      <a 
        href={`#${item.slug}`} 
        class={`block py-1 ${item.depth === 2 ? 'text-gray-700 dark:text-gray-300' : 'pl-4 text-gray-600 dark:text-gray-400'}`}
      >
        {item.text}
      </a>
    ))}
  </nav>
</div>

<style>
  .table-of-contents {
    position: sticky;
    top: 6rem;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const tocLinks = document.querySelectorAll('.table-of-contents a');
    
    // Highlight active section
    const observerOptions = {
      root: null,
      rootMargin: '0px 0px -80% 0px',
      threshold: 0
    };
    
    const headingObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Remove active class from all links
          tocLinks.forEach(link => {
            link.classList.remove('text-primary-600', 'dark:text-primary-400', 'font-medium');
          });
          
          // Add active class to current link
          const activeLink = document.querySelector(`.table-of-contents a[href="#${entry.target.id}"]`);
          if (activeLink) {
            activeLink.classList.add('text-primary-600', 'dark:text-primary-400', 'font-medium');
          }
        }
      });
    }, observerOptions);
    
    // Observe all headings
    document.querySelectorAll('h2, h3').forEach(heading => {
      headingObserver.observe(heading);
    });
    
    // Smooth scroll to section
    tocLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href')?.slice(1);
        if (targetId) {
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth'
            });
          }
        }
      });
    });
  });
</script>