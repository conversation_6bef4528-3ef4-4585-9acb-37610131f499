<script is:inline>
  // Theme toggle functionality
  const getThemePreference = () => {
    if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
      return localStorage.getItem('theme');
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };
  
  const setTheme = (theme) => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
    localStorage.setItem('theme', theme);
  };
  
  // Set theme on page load
  setTheme(getThemePreference());
  
  // Make theme toggle available globally
  window.toggleTheme = () => {
    const current = getThemePreference();
    const newTheme = current === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };
</script>