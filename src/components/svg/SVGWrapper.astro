---
// SVG Wrapper Component for theme-aware SVGs
export interface Props {
  class?: string;
  viewBox?: string;
  width?: string;
  height?: string;
  preserveAspectRatio?: string;
}

const { 
  class: className = "w-full h-full", 
  viewBox = "0 0 100 100",
  width,
  height,
  preserveAspectRatio = "xMidYMid meet"
} = Astro.props;
---

<svg 
  class={className}
  viewBox={viewBox}
  width={width}
  height={height}
  preserveAspectRatio={preserveAspectRatio}
  xmlns="http://www.w3.org/2000/svg"
  fill="none"
>
  <slot />
</svg>

<style>
  /* CSS Custom Properties for theme-aware SVGs */
  svg {
    color-scheme: light dark;
  }

  /* Primary colors */
  :root {
    --svg-primary-50: #f0f9ff;
    --svg-primary-100: #e0f2fe;
    --svg-primary-200: #bae6fd;
    --svg-primary-300: #7dd3fc;
    --svg-primary-400: #38bdf8;
    --svg-primary-500: #0ea5e9;
    --svg-primary-600: #0284c7;
    --svg-primary-700: #0369a1;
    --svg-primary-800: #075985;
    --svg-primary-900: #0c4a6e;
  }

  .dark {
    --svg-primary-50: #0c4a6e;
    --svg-primary-100: #075985;
    --svg-primary-200: #0369a1;
    --svg-primary-300: #0284c7;
    --svg-primary-400: #0ea5e9;
    --svg-primary-500: #38bdf8;
    --svg-primary-600: #7dd3fc;
    --svg-primary-700: #bae6fd;
    --svg-primary-800: #e0f2fe;
    --svg-primary-900: #f0f9ff;
  }

  /* Secondary colors */
  :root {
    --svg-secondary-50: #f8fafc;
    --svg-secondary-100: #f1f5f9;
    --svg-secondary-200: #e2e8f0;
    --svg-secondary-300: #cbd5e1;
    --svg-secondary-400: #94a3b8;
    --svg-secondary-500: #64748b;
    --svg-secondary-600: #475569;
    --svg-secondary-700: #334155;
    --svg-secondary-800: #1e293b;
    --svg-secondary-900: #0f172a;
  }

  .dark {
    --svg-secondary-50: #0f172a;
    --svg-secondary-100: #1e293b;
    --svg-secondary-200: #334155;
    --svg-secondary-300: #475569;
    --svg-secondary-400: #64748b;
    --svg-secondary-500: #94a3b8;
    --svg-secondary-600: #cbd5e1;
    --svg-secondary-700: #e2e8f0;
    --svg-secondary-800: #f1f5f9;
    --svg-secondary-900: #f8fafc;
  }

  /* Accent colors */
  :root {
    --svg-accent-50: #fefce8;
    --svg-accent-100: #fef9c3;
    --svg-accent-200: #fef08a;
    --svg-accent-300: #fde047;
    --svg-accent-400: #facc15;
    --svg-accent-500: #eab308;
    --svg-accent-600: #ca8a04;
    --svg-accent-700: #a16207;
    --svg-accent-800: #854d0e;
    --svg-accent-900: #713f12;
  }

  .dark {
    --svg-accent-50: #713f12;
    --svg-accent-100: #854d0e;
    --svg-accent-200: #a16207;
    --svg-accent-300: #ca8a04;
    --svg-accent-400: #eab308;
    --svg-accent-500: #facc15;
    --svg-accent-600: #fde047;
    --svg-accent-700: #fef08a;
    --svg-accent-800: #fef9c3;
    --svg-accent-900: #fefce8;
  }

  /* Success colors */
  :root {
    --svg-success-50: #f0fdf4;
    --svg-success-100: #dcfce7;
    --svg-success-200: #bbf7d0;
    --svg-success-300: #86efac;
    --svg-success-400: #4ade80;
    --svg-success-500: #22c55e;
    --svg-success-600: #16a34a;
    --svg-success-700: #15803d;
    --svg-success-800: #166534;
    --svg-success-900: #14532d;
  }

  /* Error colors */
  :root {
    --svg-error-50: #fef2f2;
    --svg-error-100: #fee2e2;
    --svg-error-200: #fecaca;
    --svg-error-300: #fca5a5;
    --svg-error-400: #f87171;
    --svg-error-500: #ef4444;
    --svg-error-600: #dc2626;
    --svg-error-700: #b91c1c;
    --svg-error-800: #991b1b;
    --svg-error-900: #7f1d1d;
  }

  /* Warning colors */
  :root {
    --svg-warning-50: #fffbeb;
    --svg-warning-100: #fef3c7;
    --svg-warning-200: #fde68a;
    --svg-warning-300: #fcd34d;
    --svg-warning-400: #fbbf24;
    --svg-warning-500: #f59e0b;
    --svg-warning-600: #d97706;
    --svg-warning-700: #b45309;
    --svg-warning-800: #92400e;
    --svg-warning-900: #78350f;
  }

  /* Neutral colors */
  :root {
    --svg-neutral-50: #fafafa;
    --svg-neutral-100: #f5f5f5;
    --svg-neutral-200: #e5e5e5;
    --svg-neutral-300: #d4d4d4;
    --svg-neutral-400: #a3a3a3;
    --svg-neutral-500: #737373;
    --svg-neutral-600: #525252;
    --svg-neutral-700: #404040;
    --svg-neutral-800: #262626;
    --svg-neutral-900: #171717;
  }

  .dark {
    --svg-neutral-50: #171717;
    --svg-neutral-100: #262626;
    --svg-neutral-200: #404040;
    --svg-neutral-300: #525252;
    --svg-neutral-400: #737373;
    --svg-neutral-500: #a3a3a3;
    --svg-neutral-600: #d4d4d4;
    --svg-neutral-700: #e5e5e5;
    --svg-neutral-800: #f5f5f5;
    --svg-neutral-900: #fafafa;
  }

  /* Background colors */
  :root {
    --svg-bg-primary: #ffffff;
    --svg-bg-secondary: #f8fafc;
    --svg-bg-tertiary: #f1f5f9;
  }

  .dark {
    --svg-bg-primary: #0f172a;
    --svg-bg-secondary: #1e293b;
    --svg-bg-tertiary: #334155;
  }

  /* Text colors */
  :root {
    --svg-text-primary: #0f172a;
    --svg-text-secondary: #475569;
    --svg-text-tertiary: #64748b;
  }

  .dark {
    --svg-text-primary: #f8fafc;
    --svg-text-secondary: #cbd5e1;
    --svg-text-tertiary: #94a3b8;
  }
</style>
