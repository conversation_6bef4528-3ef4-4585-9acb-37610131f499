---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  businessName?: string;
  address?: string;
  interactive?: boolean;
  showRoute?: boolean;
  estimatedTime?: string;
}

const {
  class: className = "w-full h-48",
  businessName = "Our Store",
  address = "123 Main Street",
  interactive = false,
  showRoute = true,
  estimatedTime = "15 min"
} = Astro.props;
---

<SVGWrapper 
  class={className}
  viewBox="0 0 400 300"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="map-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-success-50)" />
      <stop offset="100%" stop-color="var(--svg-success-100)" />
    </linearGradient>
    
    <linearGradient id="road-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--svg-neutral-400)" />
      <stop offset="50%" stop-color="var(--svg-neutral-300)" />
      <stop offset="100%" stop-color="var(--svg-neutral-400)" />
    </linearGradient>
    
    <filter id="building-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="2" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Animated route pattern -->
    <pattern id="route-pattern" x="0" y="0" width="20" height="4" patternUnits="userSpaceOnUse">
      <rect width="20" height="4" fill="var(--svg-primary-500)"/>
      <rect x="0" y="0" width="10" height="4" fill="var(--svg-primary-600)">
        {interactive && <animateTransform attributeName="transform" type="translate" values="0,0; 20,0; 0,0" dur="2s" repeatCount="indefinite"/>}
      </rect>
    </pattern>
  </defs>

  <!-- Map Background -->
  <rect width="400" height="300" rx="12" fill="url(#map-bg)" />
  
  <!-- Park/Green Areas -->
  <rect x="30" y="40" width="340" height="220" rx="8" fill="var(--svg-success-200)" opacity="0.6" />
  <circle cx="100" cy="100" r="25" fill="var(--svg-success-300)" opacity="0.8" />
  <circle cx="320" cy="180" r="30" fill="var(--svg-success-300)" opacity="0.8" />
  
  <!-- Roads -->
  <!-- Main Street (horizontal) -->
  <rect x="30" y="120" width="340" height="16" fill="url(#road-gradient)" />
  <line x1="30" y1="128" x2="370" y2="128" stroke="var(--svg-neutral-100)" stroke-width="2" stroke-dasharray="8 4" />
  
  <!-- Cross Street (vertical) -->
  <rect x="180" y="40" width="16" height="220" fill="url(#road-gradient)" />
  <line x1="188" y1="40" x2="188" y2="260" stroke="var(--svg-neutral-100)" stroke-width="2" stroke-dasharray="8 4" />
  
  <!-- Side Street -->
  <rect x="280" y="40" width="12" height="220" fill="var(--svg-neutral-400)" />
  
  <!-- Buildings -->
  <!-- Building 1 -->
  <rect x="50" y="60" width="40" height="50" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="55" y="65" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="67" y="65" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="79" y="65" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="55" y="85" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="67" y="85" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="79" y="85" width="8" height="8" fill="var(--svg-primary-200)" />
  
  <!-- Building 2 -->
  <rect x="200" y="50" width="60" height="60" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="210" y="60" width="12" height="12" fill="var(--svg-primary-200)" />
  <rect x="230" y="60" width="12" height="12" fill="var(--svg-primary-200)" />
  <rect x="210" y="80" width="12" height="12" fill="var(--svg-primary-200)" />
  <rect x="230" y="80" width="12" height="12" fill="var(--svg-primary-200)" />
  
  <!-- Target Store (highlighted) -->
  <rect x="200" y="150" width="60" height="50" fill="var(--svg-primary-500)" stroke="var(--svg-primary-700)" stroke-width="2" filter="url(#building-shadow)" />
  <rect x="210" y="160" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="230" y="160" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="210" y="180" width="12" height="12" fill="var(--svg-bg-primary)" />
  <rect x="230" y="180" width="12" height="12" fill="var(--svg-bg-primary)" />
  
  <!-- Store Sign -->
  <rect x="205" y="140" width="50" height="8" fill="var(--svg-accent-500)" rx="2" />
  <text x="230" y="147" font-family="system-ui" font-size="6" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">STORE</text>
  
  <!-- Store Location Pin -->
  <circle cx="230" cy="175" r="12" fill="var(--svg-error-500)" stroke="var(--svg-bg-primary)" stroke-width="2" />
  <circle cx="230" cy="175" r="6" fill="var(--svg-bg-primary)" />
  <path d="M230 172 L230 178 M227 175 L233 175" stroke="var(--svg-error-500)" stroke-width="2" />
  
  <!-- Pulsing location indicator -->
  {interactive && (
    <>
      <circle cx="230" cy="175" r="18" fill="var(--svg-error-500)" opacity="0.3" class="animate-ping" />
      <circle cx="230" cy="175" r="24" fill="var(--svg-error-500)" opacity="0.2" class="animate-ping" style="animation-delay: 0.5s" />
    </>
  )}
  
  <!-- Building 3 -->
  <rect x="300" y="60" width="50" height="50" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="310" y="70" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="330" y="70" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="310" y="90" width="10" height="10" fill="var(--svg-primary-200)" />
  <rect x="330" y="90" width="10" height="10" fill="var(--svg-primary-200)" />
  
  <!-- Building 4 -->
  <rect x="50" y="150" width="40" height="60" fill="var(--svg-neutral-200)" stroke="var(--svg-neutral-400)" stroke-width="1" filter="url(#building-shadow)" />
  <rect x="55" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="67" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  <rect x="79" y="160" width="8" height="8" fill="var(--svg-primary-200)" />
  
  <!-- Current Location -->
  <circle cx="120" cy="80" r="10" fill="var(--svg-primary-600)" stroke="var(--svg-bg-primary)" stroke-width="2" />
  <circle cx="120" cy="80" r="4" fill="var(--svg-bg-primary)" />
  
  <!-- Current location pulse -->
  {interactive && (
    <circle cx="120" cy="80" r="15" fill="var(--svg-primary-600)" opacity="0.4" class="animate-pulse" />
  )}
  
  <!-- Route Path -->
  {showRoute && (
    <>
      <path d="M120 80 L120 128 L230 128 L230 175" 
            stroke="url(#route-pattern)" 
            stroke-width="4" 
            fill="none" 
            stroke-linecap="round" 
            stroke-linejoin="round" />
      
      <!-- Route arrows -->
      <path d="M115 125 L120 128 L115 131" stroke="var(--svg-primary-600)" stroke-width="2" fill="none" stroke-linecap="round" />
      <path d="M227 170 L230 175 L233 170" stroke="var(--svg-primary-600)" stroke-width="2" fill="none" stroke-linecap="round" />
    </>
  )}
  
  <!-- Street Labels -->
  <text x="200" y="20" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="middle" font-weight="500">Main Street</text>
  <text x="150" y="35" font-family="system-ui" font-size="10" fill="var(--svg-text-tertiary)" text-anchor="middle">Cross St</text>
  
  <!-- Location Labels -->
  <text x="120" y="60" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">You are here</text>
  <text x="230" y="220" font-family="system-ui" font-size="12" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">{businessName}</text>
  <text x="230" y="235" font-family="system-ui" font-size="10" fill="var(--svg-text-secondary)" text-anchor="middle">{address}</text>
  
  <!-- Distance/Time Info -->
  {showRoute && (
    <rect x="20" y="20" width="80" height="30" fill="var(--svg-bg-primary)" stroke="var(--svg-primary-300)" stroke-width="1" rx="4" opacity="0.95" />
    <text x="30" y="35" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" font-weight="600">Distance: 0.8 mi</text>
    <text x="30" y="45" font-family="system-ui" font-size="10" fill="var(--svg-text-secondary)">Time: {estimatedTime}</text>
  )}
  
  <!-- Compass -->
  <circle cx="350" cy="50" r="20" fill="var(--svg-bg-primary)" stroke="var(--svg-neutral-300)" stroke-width="1" opacity="0.9" />
  <path d="M350 35 L355 45 L350 40 L345 45 Z" fill="var(--svg-error-500)" />
  <text x="350" y="70" font-family="system-ui" font-size="8" fill="var(--svg-text-tertiary)" text-anchor="middle">N</text>
  
  <!-- Interactive hover overlay -->
  {interactive && (
    <rect x="0" y="0" width="400" height="300" rx="12" fill="var(--svg-primary-500)" opacity="0" class="hover:opacity-5 transition-opacity duration-300 cursor-pointer" />
  )}
</SVGWrapper>
