---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  doctorName?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  appointmentType?: string;
  clinicName?: string;
  interactive?: boolean;
}

const {
  class: className = "w-full",
  doctorName = "<PERSON>. <PERSON>",
  appointmentDate = "June 15, 2025",
  appointmentTime = "2:00 PM",
  appointmentType = "Annual Checkup",
  clinicName = "HealthCare Plus",
  interactive = false
} = Astro.props;
---

<SVGWrapper 
  class={className}
  viewBox="0 0 400 280"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="calendar-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-primary-50)" />
      <stop offset="100%" stop-color="var(--svg-primary-100)" />
    </linearGradient>
    
    <linearGradient id="header-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-primary-500)" />
      <stop offset="100%" stop-color="var(--svg-primary-600)" />
    </linearGradient>
    
    <filter id="card-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Clock animation -->
    <g id="clock-hands">
      <line x1="0" y1="0" x2="0" y2="-8" stroke="var(--svg-text-primary)" stroke-width="2" stroke-linecap="round">
        {interactive && <animateTransform attributeName="transform" type="rotate" values="0;360" dur="12s" repeatCount="indefinite"/>}
      </line>
      <line x1="0" y1="0" x2="0" y2="-6" stroke="var(--svg-text-primary)" stroke-width="1.5" stroke-linecap="round">
        {interactive && <animateTransform attributeName="transform" type="rotate" values="0;30" dur="1s" repeatCount="indefinite"/>}
      </line>
    </g>
  </defs>

  <!-- Main Card Background -->
  <rect width="400" height="280" rx="16" fill="var(--svg-bg-primary)" filter="url(#card-shadow)" />
  
  <!-- Header Section -->
  <rect x="0" y="0" width="400" height="60" rx="16" fill="url(#header-gradient)" />
  <rect x="0" y="44" width="400" height="16" fill="url(#header-gradient)" />
  
  <!-- Calendar Icon -->
  <g transform="translate(30, 15)">
    <rect x="0" y="5" width="30" height="25" rx="3" fill="var(--svg-bg-primary)" />
    <rect x="0" y="0" width="30" height="8" rx="3" fill="var(--svg-bg-primary)" />
    <rect x="5" y="-3" width="3" height="8" rx="1.5" fill="var(--svg-primary-300)" />
    <rect x="22" y="-3" width="3" height="8" rx="1.5" fill="var(--svg-primary-300)" />
    
    <!-- Calendar grid -->
    <line x1="5" y1="12" x2="25" y2="12" stroke="var(--svg-primary-200)" stroke-width="0.5" />
    <line x1="5" y1="17" x2="25" y2="17" stroke="var(--svg-primary-200)" stroke-width="0.5" />
    <line x1="5" y1="22" x2="25" y2="22" stroke="var(--svg-primary-200)" stroke-width="0.5" />
    
    <!-- Date highlight -->
    <rect x="18" y="15" width="4" height="4" rx="1" fill="var(--svg-error-500)" />
    <text x="20" y="18.5" font-family="system-ui" font-size="3" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">15</text>
  </g>
  
  <!-- Header Text -->
  <text x="80" y="35" font-family="system-ui" font-size="18" fill="var(--svg-bg-primary)" font-weight="bold">Appointment Reminder</text>
  <text x="80" y="50" font-family="system-ui" font-size="12" fill="var(--svg-bg-primary)" opacity="0.9">{clinicName}</text>
  
  <!-- Status Indicator -->
  <circle cx="350" cy="30" r="8" fill="var(--svg-success-500)" />
  <path d="M346 30 L349 33 L354 27" stroke="var(--svg-bg-primary)" stroke-width="2" fill="none" stroke-linecap="round" />
  
  <!-- Main Content Area -->
  <rect x="20" y="80" width="360" height="180" rx="12" fill="url(#calendar-bg)" />
  
  <!-- Doctor Info Section -->
  <g transform="translate(40, 100)">
    <!-- Doctor Avatar -->
    <circle cx="25" cy="25" r="20" fill="var(--svg-primary-200)" />
    <circle cx="25" cy="20" r="8" fill="var(--svg-primary-400)" />
    <path d="M10 40 C10 35 15 30 25 30 C35 30 40 35 40 40" fill="var(--svg-primary-400)" />
    
    <!-- Stethoscope -->
    <circle cx="35" cy="15" r="3" fill="var(--svg-primary-600)" />
    <path d="M35 18 Q40 20 40 25" stroke="var(--svg-primary-600)" stroke-width="2" fill="none" />
  </g>
  
  <!-- Doctor Details -->
  <text x="100" y="115" font-family="system-ui" font-size="16" fill="var(--svg-text-primary)" font-weight="bold">{doctorName}</text>
  <text x="100" y="135" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">{appointmentType}</text>
  
  <!-- Appointment Details -->
  <g transform="translate(40, 160)">
    <!-- Date Icon -->
    <rect x="0" y="0" width="20" height="16" rx="2" fill="var(--svg-primary-500)" />
    <rect x="2" y="2" width="16" height="12" rx="1" fill="var(--svg-bg-primary)" />
    <rect x="4" y="-2" width="2" height="6" rx="1" fill="var(--svg-primary-300)" />
    <rect x="14" y="-2" width="2" height="6" rx="1" fill="var(--svg-primary-300)" />
    <text x="10" y="12" font-family="system-ui" font-size="8" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="bold">15</text>
    
    <text x="30" y="12" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">{appointmentDate}</text>
  </g>
  
  <g transform="translate(40, 185)">
    <!-- Clock Icon -->
    <circle cx="10" cy="8" r="8" fill="var(--svg-primary-500)" />
    <circle cx="10" cy="8" r="6" fill="var(--svg-bg-primary)" />
    <use href="#clock-hands" transform="translate(10, 8)" />
    <circle cx="10" cy="8" r="1" fill="var(--svg-text-primary)" />
    
    <text x="30" y="12" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">{appointmentTime}</text>
  </g>
  
  <!-- Location Info -->
  <g transform="translate(40, 210)">
    <!-- Location Pin -->
    <path d="M10 0 C15 0 18 3 18 8 C18 12 10 20 10 20 C10 20 2 12 2 8 C2 3 5 0 10 0 Z" fill="var(--svg-error-500)" />
    <circle cx="10" cy="8" r="3" fill="var(--svg-bg-primary)" />
    
    <text x="30" y="8" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">123 Medical Center Dr.</text>
    <text x="30" y="20" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">Suite 200, Health City</text>
  </g>
  
  <!-- Action Buttons -->
  <g transform="translate(250, 160)">
    <!-- Confirm Button -->
    <rect x="0" y="0" width="80" height="30" rx="15" fill="var(--svg-success-500)" />
    <text x="40" y="18" font-family="system-ui" font-size="12" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="600">Confirm</text>
    
    <!-- Reschedule Button -->
    <rect x="0" y="40" width="80" height="30" rx="15" fill="var(--svg-secondary-300)" stroke="var(--svg-secondary-400)" stroke-width="1" />
    <text x="40" y="58" font-family="system-ui" font-size="12" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Reschedule</text>
  </g>
  
  <!-- Notification Badge -->
  <circle cx="370" cy="90" r="12" fill="var(--svg-error-500)" />
  <text x="370" y="95" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">!</text>
  
  <!-- Reminder Text -->
  <text x="200" y="250" font-family="system-ui" font-size="11" fill="var(--svg-text-tertiary)" text-anchor="middle">Reminder: Please arrive 15 minutes early</text>
  
  <!-- Pulse animation for notification -->
  {interactive && (
    <>
      <circle cx="370" cy="90" r="18" fill="var(--svg-error-500)" opacity="0.3" class="animate-ping" />
      <circle cx="370" cy="90" r="24" fill="var(--svg-error-500)" opacity="0.2" class="animate-ping" style="animation-delay: 0.5s" />
    </>
  )}
  
  <!-- Interactive hover effect -->
  {interactive && (
    <rect x="0" y="0" width="400" height="280" rx="16" fill="var(--svg-primary-500)" opacity="0" class="hover:opacity-5 transition-opacity duration-300 cursor-pointer" />
  )}
</SVGWrapper>
