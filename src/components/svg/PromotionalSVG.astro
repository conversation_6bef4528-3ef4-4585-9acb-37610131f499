---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  title?: string;
  subtitle?: string;
  discount?: string;
  ctaText?: string;
  brandName?: string;
  interactive?: boolean;
  theme?: 'sale' | 'new' | 'limited' | 'seasonal';
  fullScreen?: boolean;
}

const {
  class: className = "w-full h-full",
  title = "FLASH SALE",
  subtitle = "Limited Time Offer",
  discount = "50% OFF",
  ctaText = "Shop Now",
  brandName = "Fashion Store",
  interactive = false,
  theme = 'sale',
  fullScreen = false
} = Astro.props;

// Theme-based colors
const themeColors = {
  sale: {
    primary: 'var(--svg-error-500)',
    secondary: 'var(--svg-error-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-error-50)'
  },
  new: {
    primary: 'var(--svg-primary-500)',
    secondary: 'var(--svg-primary-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-primary-50)'
  },
  limited: {
    primary: 'var(--svg-warning-500)',
    secondary: 'var(--svg-warning-600)',
    accent: 'var(--svg-error-400)',
    bg: 'var(--svg-warning-50)'
  },
  seasonal: {
    primary: 'var(--svg-success-500)',
    secondary: 'var(--svg-success-600)',
    accent: 'var(--svg-accent-400)',
    bg: 'var(--svg-success-50)'
  }
};

const colors = themeColors[theme];
---

<SVGWrapper
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="promo-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color={colors.primary} />
      <stop offset="100%" stop-color={colors.secondary} />
    </linearGradient>
    
    <linearGradient id="promo-accent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color={colors.accent} stop-opacity="0.8" />
      <stop offset="50%" stop-color={colors.accent} stop-opacity="1" />
      <stop offset="100%" stop-color={colors.accent} stop-opacity="0.8" />
    </linearGradient>
    
    <radialGradient id="spotlight" cx="50%" cy="30%" r="60%">
      <stop offset="0%" stop-color="var(--svg-bg-primary)" stop-opacity="0.3" />
      <stop offset="100%" stop-color="var(--svg-bg-primary)" stop-opacity="0" />
    </radialGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="0" dy="0"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.8"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Sparkle pattern -->
    <pattern id="sparkles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="var(--svg-bg-primary)" opacity="0.6">
        {interactive && <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>}
      </circle>
      <circle cx="30" cy="25" r="1.5" fill="var(--svg-bg-primary)" opacity="0.4">
        {interactive && <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>}
      </circle>
      <circle cx="5" cy="35" r="0.8" fill="var(--svg-bg-primary)" opacity="0.7">
        {interactive && <animate attributeName="opacity" values="0.7;1;0.7" dur="1.5s" repeatCount="indefinite"/>}
      </circle>
    </pattern>
  </defs>

  <!-- Main Background -->
  <rect width="400" height="250" rx="16" fill="url(#promo-bg)" />
  
  <!-- Spotlight Effect -->
  <rect width="400" height="250" rx="16" fill="url(#spotlight)" />
  
  <!-- Sparkle Overlay -->
  <rect width="400" height="250" rx="16" fill="url(#sparkles)" opacity="0.3" />
  
  <!-- Decorative Elements -->
  <!-- Top Banner -->
  <rect x="0" y="20" width="400" height="40" fill="url(#promo-accent)" opacity="0.9" />
  <rect x="0" y="20" width="400" height="4" fill="var(--svg-bg-primary)" opacity="0.8" />
  <rect x="0" y="56" width="400" height="4" fill="var(--svg-bg-primary)" opacity="0.8" />
  
  <!-- Corner Decorations -->
  <circle cx="30" cy="30" r="8" fill="var(--svg-bg-primary)" opacity="0.2" />
  <circle cx="370" cy="30" r="8" fill="var(--svg-bg-primary)" opacity="0.2" />
  <circle cx="30" cy="220" r="8" fill="var(--svg-bg-primary)" opacity="0.2" />
  <circle cx="370" cy="220" r="8" fill="var(--svg-bg-primary)" opacity="0.2" />
  
  <!-- Brand Section -->
  <text x="200" y="45" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="600">{brandName}</text>
  
  <!-- Main Content -->
  <!-- Discount Badge -->
  <circle cx="100" cy="120" r="45" fill="var(--svg-accent-500)" stroke="var(--svg-bg-primary)" stroke-width="3" filter="url(#glow)" />
  <text x="100" y="115" font-family="system-ui" font-size="18" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="bold">{discount}</text>
  <text x="100" y="130" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle">DISCOUNT</text>
  
  <!-- Pulsing effect for discount badge -->
  {interactive && (
    <circle cx="100" cy="120" r="50" fill="var(--svg-accent-500)" opacity="0.3" class="animate-pulse" />
  )}
  
  <!-- Main Text -->
  <text x="250" y="100" font-family="system-ui" font-size="28" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">{title}</text>
  <text x="250" y="125" font-family="system-ui" font-size="16" fill="var(--svg-bg-primary)" text-anchor="middle" opacity="0.9">{subtitle}</text>
  
  <!-- Product Icons -->
  <!-- Shirt Icon -->
  <g transform="translate(180, 140)">
    <rect x="0" y="10" width="20" height="25" rx="2" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="5" y="0" width="10" height="15" rx="5" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="-2" y="12" width="8" height="15" rx="2" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="14" y="12" width="8" height="15" rx="2" fill="var(--svg-bg-primary)" opacity="0.8" />
  </g>
  
  <!-- Bag Icon -->
  <g transform="translate(220, 140)">
    <rect x="0" y="8" width="20" height="25" rx="2" fill="var(--svg-bg-primary)" opacity="0.8" />
    <path d="M5 8 L5 5 C5 2 7 0 10 0 C13 0 15 2 15 5 L15 8" stroke="var(--svg-bg-primary)" stroke-width="2" fill="none" opacity="0.8" />
  </g>
  
  <!-- Shoe Icon -->
  <g transform="translate(260, 140)">
    <path d="M0 20 C0 15 5 10 10 10 L15 10 C18 10 20 12 20 15 L20 20 C20 25 15 30 10 30 L5 30 C2 30 0 25 0 20 Z" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="5" y="15" width="10" height="3" fill={colors.primary} opacity="0.6" />
  </g>
  
  <!-- Watch Icon -->
  <g transform="translate(300, 140)">
    <circle cx="10" cy="20" r="8" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="8" y="5" width="4" height="8" fill="var(--svg-bg-primary)" opacity="0.8" />
    <rect x="8" y="27" width="4" height="8" fill="var(--svg-bg-primary)" opacity="0.8" />
    <circle cx="10" cy="20" r="6" fill={colors.primary} opacity="0.3" />
  </g>
  
  <!-- CTA Button -->
  <rect x="150" y="190" width="100" height="35" rx="18" fill="var(--svg-accent-500)" stroke="var(--svg-bg-primary)" stroke-width="2" filter="url(#glow)" />
  <text x="200" y="210" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="bold">{ctaText}</text>
  
  <!-- Arrow in CTA -->
  <path d="M235 205 L245 208 L235 211" stroke="var(--svg-text-primary)" stroke-width="2" fill="none" stroke-linecap="round" />
  
  <!-- Urgency Indicator -->
  {theme === 'limited' && (
    <g transform="translate(320, 80)">
      <circle cx="0" cy="0" r="12" fill="var(--svg-error-500)" />
      <text x="0" y="4" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">!</text>
      <text x="0" y="25" font-family="system-ui" font-size="8" fill="var(--svg-bg-primary)" text-anchor="middle">LIMITED</text>
    </g>
  )}
  
  <!-- Timer for flash sales -->
  {theme === 'sale' && (
    <g transform="translate(320, 190)">
      <rect x="-25" y="-10" width="50" height="20" rx="10" fill="var(--svg-bg-primary)" opacity="0.9" />
      <text x="0" y="2" font-family="system-ui" font-size="10" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">23:59:45</text>
      <text x="0" y="25" font-family="system-ui" font-size="8" fill="var(--svg-bg-primary)" text-anchor="middle">TIME LEFT</text>
    </g>
  )}
  
  <!-- Decorative Stars -->
  <g opacity="0.6">
    <path d="M50 80 L52 85 L57 85 L53 88 L55 93 L50 90 L45 93 L47 88 L43 85 L48 85 Z" fill="var(--svg-bg-primary)" />
    <path d="M350 160 L352 165 L357 165 L353 168 L355 173 L350 170 L345 173 L347 168 L343 165 L348 165 Z" fill="var(--svg-bg-primary)" />
  </g>
  
  <!-- Shimmer effect overlay -->
  {interactive && (
    <rect x="0" y="0" width="100" height="250" rx="16" fill="url(#promo-accent)" opacity="0.3" class="animate-shimmer" />
  )}

  <!-- Interactive hover effect -->
  {interactive && (
    <rect x="0" y="0" width="400" height="250" rx="16" fill="var(--svg-bg-primary)" opacity="0" class="hover:opacity-10 transition-opacity duration-300 cursor-pointer" />
  )}
</SVGWrapper>

{interactive && (
  <style>
    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(400px); }
    }

    .animate-shimmer {
      animation: shimmer 3s ease-in-out infinite;
    }
  </style>
)}
