---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  eventName?: string;
  eventDate?: string;
  eventLocation?: string;
  ticketType?: string;
  ticketNumber?: string;
  holderName?: string;
  gate?: string;
  section?: string;
  interactive?: boolean;
  fullScreen?: boolean;
}

const {
  class: className = "w-full h-full",
  eventName = "SUMMER MUSIC FESTIVAL",
  eventDate = "July 15-17, 2025",
  eventLocation = "Central Park",
  ticketType = "VIP Pass",
  ticketNumber = "VIP-2025-78945",
  holderName = "<PERSON>",
  gate = "GATE 4",
  section = "SECTION A",
  interactive = false,
  fullScreen = false
} = Astro.props;
---

<SVGWrapper
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="ticket-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-primary-500)" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="var(--svg-primary-500)" stop-opacity="0.3"/>
    </linearGradient>

    <linearGradient id="message-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-bg-primary)"/>
      <stop offset="100%" stop-color="var(--svg-bg-secondary)"/>
    </linearGradient>

    <filter id="card-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- QR Code Pattern -->
    <pattern id="qr-pattern" x="0" y="0" width="8" height="8" patternUnits="userSpaceOnUse">
      <rect width="8" height="8" fill="var(--svg-neutral-200)"/>
      <rect x="0" y="0" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="5" y="0" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="2" y="2" width="4" height="4" fill="var(--svg-neutral-800)"/>
      <rect x="0" y="5" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="5" y="5" width="3" height="3" fill="var(--svg-neutral-800)"/>
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="var(--svg-primary-500)"/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">Event Center</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="50" rx="16" fill="url(#message-bg)" filter="url(#card-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Hi! Ready to get your tickets for</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" font-weight="600">{eventName}? 🎫</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(64, 160)">
    <rect width="280" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#card-shadow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Yes! I need 2 VIP tickets please</text>
  </g>

  <!-- Business Response -->
  <g transform="translate(16, 210)">
    <rect width="300" height="50" rx="16" fill="url(#message-bg)" filter="url(#card-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Perfect! Here are your VIP tickets.</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">They include backstage access! 🌟</text>
  </g>

  <!-- Ticket Card -->
  <g transform="translate(16, 280)">
    <rect width="328" height="180" rx="16" fill="url(#ticket-bg)" filter="url(#card-shadow)"/>

    <!-- Ticket Content -->
    <rect x="16" y="16" width="296" height="80" rx="8" fill="var(--svg-bg-primary)" opacity="0.95" />

    <!-- Event Details -->
    <text x="32" y="40" font-family="system-ui" font-weight="bold" font-size="14" fill="var(--svg-text-primary)">{eventName}</text>
    <text x="32" y="58" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">{eventDate} • {eventLocation}</text>
    <text x="32" y="76" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">{ticketType}</text>

    <!-- QR Code -->
    <rect x="240" y="24" width="56" height="56" rx="4" fill="url(#qr-pattern)" stroke="var(--svg-neutral-300)" stroke-width="1" />
    <text x="268" y="58" font-family="system-ui" font-size="8" fill="var(--svg-text-tertiary)" text-anchor="middle" font-weight="600">SCAN</text>

    <!-- Ticket Details -->
    <line x1="32" y1="110" x2="312" y2="110" stroke="var(--svg-neutral-300)" stroke-width="1" stroke-dasharray="4 2" opacity="0.8" />

    <text x="32" y="135" font-family="system-ui" font-weight="bold" font-size="12" fill="var(--svg-text-primary)">TICKET #: {ticketNumber}</text>
    <text x="32" y="155" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)">{holderName}</text>

    <text x="280" y="135" font-family="system-ui" font-weight="bold" font-size="12" fill="var(--svg-text-primary)" text-anchor="end">{gate}</text>
    <text x="280" y="155" font-family="system-ui" font-size="12" fill="var(--svg-text-secondary)" text-anchor="end">{section}</text>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 480)">
    <rect width="160" height="40" rx="20" fill="var(--svg-accent-500)" filter="url(#card-shadow)"/>
    <text x="80" y="26" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Add to Wallet</text>

    <rect x="172" y="0" width="156" height="40" rx="20" fill="var(--svg-secondary-300)" stroke="var(--svg-secondary-400)" stroke-width="1"/>
    <text x="250" y="26" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Share Ticket</text>
  </g>

  <!-- Customer Response -->
  <g transform="translate(80, 540)">
    <rect width="264" height="35" rx="16" fill="var(--svg-secondary-100)" filter="url(#card-shadow)"/>
    <text x="20" y="23" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Amazing! Added to my wallet. Thank you! 🎉</text>
  </g>

  <!-- Typing Indicator -->
  <g transform="translate(16, 590)">
    <rect width="80" height="30" rx="15" fill="var(--svg-secondary-200)" opacity="0.8"/>
    <circle cx="30" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>}
    </circle>
    <circle cx="40" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>}
    </circle>
    <circle cx="50" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>}
    </circle>
  </g>

  <!-- Interactive elements -->
  {interactive && (
    <>
      <!-- Pulse Animation for QR Code -->
      <circle cx="268" cy="332" r="35" fill="var(--svg-accent-400)" opacity="0.2" class="animate-ping" />

      <!-- Notification badge -->
      <g transform="translate(320, 20)">
        <circle cx="0" cy="0" r="8" fill="var(--svg-success-500)"/>
        <text x="0" y="4" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">2</text>
      </g>
    </>
  )}
</SVGWrapper>

{interactive && (
  <style>
    @keyframes pulse-glow {
      0%, 100% { opacity: 0.2; transform: scale(1); }
      50% { opacity: 0.4; transform: scale(1.05); }
    }
    
    .animate-pulse-glow {
      animation: pulse-glow 2s ease-in-out infinite;
    }
  </style>
)}
