---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  eventName?: string;
  eventDate?: string;
  eventLocation?: string;
  ticketType?: string;
  ticketNumber?: string;
  holderName?: string;
  gate?: string;
  section?: string;
  interactive?: boolean;
  fullScreen?: boolean;
}

const {
  class: className = "w-full",
  eventName = "SUMMER MUSIC FESTIVAL",
  eventDate = "July 15-17, 2025",
  eventLocation = "Central Park",
  ticketType = "3-Day Pass • General Admission",
  ticketNumber = "VIP-2025-78945",
  holderName = "<PERSON>",
  gate = "GATE 4",
  section = "SECTION A",
  interactive = false,
  fullScreen = false
} = Astro.props;
---

<SVGWrapper 
  class={className}
  viewBox="0 0 400 220"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="ticket-gradient" x1="0" y1="0" x2="400" y2="220" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="var(--svg-primary-600)" />
      <stop offset="100%" stop-color="var(--svg-primary-800)" />
    </linearGradient>
    
    <linearGradient id="ticket-highlight" x1="0" y1="0" x2="100%" y2="0" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="var(--svg-accent-400)" stop-opacity="0.8" />
      <stop offset="100%" stop-color="var(--svg-accent-600)" stop-opacity="0.9" />
    </linearGradient>
    
    <filter id="ticket-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="0" dy="4"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- QR Code Pattern -->
    <pattern id="qr-pattern" x="0" y="0" width="8" height="8" patternUnits="userSpaceOnUse">
      <rect width="8" height="8" fill="var(--svg-neutral-200)"/>
      <rect x="0" y="0" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="5" y="0" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="2" y="2" width="4" height="4" fill="var(--svg-neutral-800)"/>
      <rect x="0" y="5" width="3" height="3" fill="var(--svg-neutral-800)"/>
      <rect x="5" y="5" width="3" height="3" fill="var(--svg-neutral-800)"/>
    </pattern>
  </defs>

  <!-- Main Ticket Background -->
  <rect width="400" height="220" rx="12" fill="url(#ticket-gradient)" filter="url(#ticket-shadow)" />
  
  <!-- Top Section Background -->
  <rect x="20" y="20" width="360" height="100" rx="8" fill="var(--svg-bg-primary)" fill-opacity="0.95" />
  
  <!-- Event Details -->
  <text x="40" y="45" font-family="system-ui" font-weight="bold" font-size="16" fill="var(--svg-text-primary)">{eventName}</text>
  <text x="40" y="70" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">{eventDate} • {eventLocation}</text>
  <text x="40" y="95" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">{ticketType}</text>
  
  <!-- QR Code -->
  <rect x="280" y="30" width="80" height="80" rx="4" fill="url(#qr-pattern)" stroke="var(--svg-neutral-300)" stroke-width="1" />
  <rect x="290" y="40" width="60" height="60" rx="2" fill="var(--svg-bg-primary)" />
  <text x="320" y="75" font-family="system-ui" font-size="10" fill="var(--svg-text-tertiary)" text-anchor="middle" font-weight="600">SCAN ME</text>
  
  <!-- Decorative Elements -->
  <circle cx="50" cy="50" r="3" fill="var(--svg-accent-500)" opacity="0.6" />
  <circle cx="350" cy="50" r="3" fill="var(--svg-accent-500)" opacity="0.6" />
  <circle cx="50" cy="90" r="2" fill="var(--svg-accent-400)" opacity="0.4" />
  <circle cx="350" cy="90" r="2" fill="var(--svg-accent-400)" opacity="0.4" />
  
  <!-- Perforated Line -->
  <line x1="20" y1="140" x2="380" y2="140" stroke="var(--svg-neutral-300)" stroke-width="2" stroke-dasharray="6 4" opacity="0.8" />
  
  <!-- Bottom Section -->
  <rect x="20" y="140" width="360" height="60" fill="url(#ticket-highlight)" opacity="0.1" />
  
  <!-- Ticket Details -->
  <text x="40" y="165" font-family="system-ui" font-weight="bold" font-size="14" fill="var(--svg-bg-primary)">TICKET #: {ticketNumber}</text>
  <text x="40" y="190" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" opacity="0.9">{holderName}</text>
  
  <text x="340" y="165" font-family="system-ui" font-weight="bold" font-size="14" fill="var(--svg-bg-primary)" text-anchor="end">{gate}</text>
  <text x="340" y="190" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="end" opacity="0.9">{section}</text>
  
  <!-- Security Features -->
  <rect x="15" y="15" width="370" height="190" rx="8" fill="none" stroke="var(--svg-accent-400)" stroke-width="1" opacity="0.3" stroke-dasharray="2 2" />
  
  <!-- Corner Decorations -->
  <path d="M20 20 L30 20 L20 30 Z" fill="var(--svg-accent-500)" opacity="0.7" />
  <path d="M380 20 L370 20 L380 30 Z" fill="var(--svg-accent-500)" opacity="0.7" />
  <path d="M20 200 L30 200 L20 190 Z" fill="var(--svg-accent-500)" opacity="0.7" />
  <path d="M380 200 L370 200 L380 190 Z" fill="var(--svg-accent-500)" opacity="0.7" />

  {interactive && (
    <>
      <!-- Interactive Hover Effects -->
      <rect x="0" y="0" width="400" height="220" rx="12" fill="var(--svg-primary-500)" opacity="0" class="hover:opacity-10 transition-opacity duration-300 cursor-pointer" />
      
      <!-- Pulse Animation for QR Code -->
      <circle cx="320" cy="70" r="45" fill="var(--svg-accent-400)" opacity="0.2" class="animate-ping" />
    </>
  )}
</SVGWrapper>

{interactive && (
  <style>
    @keyframes pulse-glow {
      0%, 100% { opacity: 0.2; transform: scale(1); }
      50% { opacity: 0.4; transform: scale(1.05); }
    }
    
    .animate-pulse-glow {
      animation: pulse-glow 2s ease-in-out infinite;
    }
  </style>
)}
