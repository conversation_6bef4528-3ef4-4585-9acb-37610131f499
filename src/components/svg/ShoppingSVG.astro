---
import SVGWrapper from './SVGWrapper.astro';

export interface Props {
  class?: string;
  businessName?: string;
  interactive?: boolean;
}

const {
  class: className = "w-full h-full",
  businessName = "Business Chat",
  interactive = false
} = Astro.props;
---

<SVGWrapper 
  class={className}
  viewBox="0 0 360 640"
>
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="shopping-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-primary-500)" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="var(--svg-primary-500)" stop-opacity="0.3"/>
    </linearGradient>
    
    <linearGradient id="message-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="var(--svg-bg-primary)"/>
      <stop offset="100%" stop-color="var(--svg-bg-secondary)"/>
    </linearGradient>
    
    <filter id="card-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="var(--svg-bg-secondary)"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="var(--svg-primary-500)"/>
  <circle cx="40" cy="40" r="20" fill="var(--svg-bg-primary)" opacity="0.9"/>
  <text x="80" y="45" fill="var(--svg-bg-primary)" font-family="system-ui" font-size="18" font-weight="600">{businessName}</text>
  <text x="80" y="65" fill="var(--svg-bg-primary)" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="70" rx="16" fill="url(#message-bg)" filter="url(#card-shadow)"/>
    <text x="20" y="30" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">Welcome to RCS Business Messaging! 👋</text>
    <text x="20" y="50" font-family="system-ui" font-size="14" fill="var(--svg-text-secondary)">How can we assist you today?</text>
  </g>

  <!-- Quick Reply Buttons -->
  <g transform="translate(16, 180)">
    <rect width="120" height="36" rx="18" fill="var(--svg-primary-500)" filter="url(#card-shadow)"/>
    <text x="60" y="24" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle">Products 🛍️</text>
  </g>
  <g transform="translate(146, 180)">
    <rect width="120" height="36" rx="18" fill="var(--svg-primary-500)" filter="url(#card-shadow)"/>
    <text x="60" y="24" font-family="system-ui" font-size="14" fill="var(--svg-bg-primary)" text-anchor="middle">Support 💬</text>
  </g>

  <!-- Product Carousel -->
  <g transform="translate(16, 240)">
    <rect width="328" height="200" rx="16" fill="url(#shopping-bg)" filter="url(#card-shadow)"/>

    <!-- Product Cards -->
    <g transform="translate(16, 16)">
      <rect width="140" height="168" rx="12" fill="var(--svg-bg-primary)" filter="url(#card-shadow)"/>
      <rect width="140" height="100" rx="8" fill="var(--svg-secondary-200)"/>
      
      <!-- Product Image Placeholder -->
      <g transform="translate(50, 30)">
        <rect width="40" height="40" rx="4" fill="var(--svg-primary-200)"/>
        <circle cx="20" cy="20" r="12" fill="var(--svg-primary-400)"/>
        <path d="M12 16 L20 24 L28 16" stroke="var(--svg-bg-primary)" stroke-width="2" fill="none"/>
      </g>
      
      <text x="70" y="140" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Smart Watch</text>
      <text x="70" y="160" font-family="system-ui" font-size="14" fill="var(--svg-primary-500)" text-anchor="middle" font-weight="bold">$299.99</text>
    </g>

    <g transform="translate(172, 16)">
      <rect width="140" height="168" rx="12" fill="var(--svg-bg-primary)" filter="url(#card-shadow)"/>
      <rect width="140" height="100" rx="8" fill="var(--svg-secondary-200)"/>
      
      <!-- Product Image Placeholder -->
      <g transform="translate(50, 30)">
        <rect width="40" height="40" rx="4" fill="var(--svg-accent-200)"/>
        <circle cx="20" cy="20" r="12" fill="var(--svg-accent-400)"/>
        <path d="M12 12 L28 28 M28 12 L12 28" stroke="var(--svg-bg-primary)" stroke-width="2"/>
      </g>
      
      <text x="70" y="140" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Headphones</text>
      <text x="70" y="160" font-family="system-ui" font-size="14" fill="var(--svg-primary-500)" text-anchor="middle" font-weight="bold">$149.99</text>
    </g>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 460)">
    <rect width="328" height="50" rx="25" fill="var(--svg-accent-500)" filter="url(#card-shadow)"/>
    <text x="164" y="32" font-family="system-ui" font-size="16" fill="var(--svg-text-primary)" text-anchor="middle" font-weight="600">Shop Now</text>
    
    <!-- Interactive pulse effect -->
    {interactive && (
      <rect width="328" height="50" rx="25" fill="var(--svg-accent-500)" opacity="0.3" class="animate-pulse"/>
    )}
  </g>

  <!-- Customer Message -->
  <g transform="translate(80, 530)">
    <rect width="264" height="50" rx="16" fill="var(--svg-secondary-100)" filter="url(#card-shadow)"/>
    <text x="20" y="25" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">I'd like to see more details about</text>
    <text x="20" y="40" font-family="system-ui" font-size="14" fill="var(--svg-text-primary)">the smart watch please!</text>
  </g>

  <!-- Typing Indicator -->
  <g transform="translate(16, 590)">
    <rect width="80" height="30" rx="15" fill="var(--svg-secondary-200)" opacity="0.8"/>
    <circle cx="30" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>}
    </circle>
    <circle cx="40" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>}
    </circle>
    <circle cx="50" cy="15" r="3" fill="var(--svg-text-secondary)">
      {interactive && <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>}
    </circle>
  </g>

  <!-- Interactive elements -->
  {interactive && (
    <>
      <!-- Floating notification -->
      <g transform="translate(300, 100)" class="animate-pulse">
        <circle cx="0" cy="0" r="8" fill="var(--svg-error-500)"/>
        <text x="0" y="4" font-family="system-ui" font-size="10" fill="var(--svg-bg-primary)" text-anchor="middle" font-weight="bold">3</text>
      </g>
      
      <!-- Shimmer effect on products -->
      <rect x="32" y="256" width="140" height="168" rx="12" fill="url(#shopping-bg)" opacity="0.3" class="animate-pulse"/>
      <rect x="188" y="256" width="140" height="168" rx="12" fill="url(#shopping-bg)" opacity="0.3" class="animate-pulse" style="animation-delay: 0.5s"/>
    </>
  )}

  <!-- Status indicators -->
  <g transform="translate(320, 20)">
    <circle cx="0" cy="0" r="4" fill="var(--svg-success-500)"/>
    {interactive && <circle cx="0" cy="0" r="8" fill="var(--svg-success-500)" opacity="0.3" class="animate-ping"/>}
  </g>
</SVGWrapper>
