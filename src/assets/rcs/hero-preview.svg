<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 640">
  <defs>
    <linearGradient id="hero-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:0.3"/>
    </linearGradient>
    <linearGradient id="message-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff"/>
      <stop offset="100%" style="stop-color:#f8fafc"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="360" height="640" fill="#f8fafc"/>

  <!-- Header -->
  <rect x="0" y="0" width="360" height="80" fill="#0ea5e9"/>
  <circle cx="40" cy="40" r="20" fill="#ffffff" opacity="0.9"/>
  <text x="80" y="45" fill="#ffffff" font-family="system-ui" font-size="18" font-weight="600">Business Chat</text>
  <text x="80" y="65" fill="#ffffff" opacity="0.8" font-family="system-ui" font-size="12">Online</text>

  <!-- Welcome Message -->
  <g transform="translate(16, 96)">
    <rect width="280" height="70" rx="16" fill="url(#message-grad)" filter="url(#shadow)"/>
    <text x="20" y="30" font-family="system-ui" font-size="14" fill="#1f2937">Welcome to RCS Business Messaging! 👋</text>
    <text x="20" y="50" font-family="system-ui" font-size="14" fill="#64748b">How can we assist you today?</text>
  </g>

  <!-- Quick Reply Buttons -->
  <g transform="translate(16, 180)">
    <rect width="120" height="36" rx="18" fill="#0ea5e9" filter="url(#shadow)"/>
    <text x="60" y="24" font-family="system-ui" font-size="14" fill="#ffffff" text-anchor="middle">Products 🛙️</text>
  </g>
  <g transform="translate(146, 180)">
    <rect width="120" height="36" rx="18" fill="#0ea5e9" filter="url(#shadow)"/>
    <text x="60" y="24" font-family="system-ui" font-size="14" fill="#ffffff" text-anchor="middle">Support 💬</text>
  </g>

  <!-- Product Carousel -->
  <g transform="translate(16, 240)">
    <rect width="328" height="200" rx="16" fill="url(#hero-grad)" filter="url(#shadow)"/>

    <!-- Product Cards -->
    <g transform="translate(16, 16)">
      <rect width="140" height="168" rx="12" fill="#ffffff"/>
      <rect width="140" height="100" rx="8" fill="#e2e8f0"/>
      <text x="70" y="140" font-family="system-ui" font-size="14" fill="#1f2937" text-anchor="middle">Product A</text>
      <text x="70" y="160" font-family="system-ui" font-size="14" fill="#0ea5e9" text-anchor="middle">$99.99</text>
    </g>

    <g transform="translate(172, 16)">
      <rect width="140" height="168" rx="12" fill="#ffffff"/>
      <rect width="140" height="100" rx="8" fill="#e2e8f0"/>
      <text x="70" y="140" font-family="system-ui" font-size="14" fill="#1f2937" text-anchor="middle">Product B</text>
      <text x="70" y="160" font-family="system-ui" font-size="14" fill="#0ea5e9" text-anchor="middle">$149.99</text>
    </g>
  </g>

  <!-- Action Buttons -->
  <g transform="translate(16, 460)">
    <rect width="328" height="50" rx="25" fill="#eab308" filter="url(#shadow)"/>
    <text x="164" y="32" font-family="system-ui" font-size="16" fill="#ffffff" text-anchor="middle" font-weight="600">Shop Now</text>
  </g>

  <!-- Typing Indicator -->
  <g transform="translate(16, 530)">
    <rect width="80" height="30" rx="15" fill="#e2e8f0" opacity="0.8"/>
    <circle cx="30" cy="15" r="3" fill="#64748b">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="40" cy="15" r="3" fill="#64748b">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
    </circle>
    <circle cx="50" cy="15" r="3" fill="#64748b">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
  </g>

  <!-- Bottom Navigation -->
  <g transform="translate(0, 580)">
    <rect width="360" height="60" fill="#ffffff" filter="url(#shadow)"/>
    <line x1="0" y1="580" x2="360" y2="580" stroke="#e2e8f0" stroke-width="1"/>

    <!-- Nav Items -->
    <g transform="translate(40, 600)">
      <circle cx="0" cy="0" r="4" fill="#0ea5e9"/>
      <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Home</text>
    </g>
    <g transform="translate(140, 600)">
      <circle cx="0" cy="0" r="4" fill="#64748b"/>
      <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Cart</text>
    </g>
    <g transform="translate(240, 600)">
      <circle cx="0" cy="0" r="4" fill="#64748b"/>
      <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">Profile</text>
    </g>
    <g transform="translate(320, 600)">
      <circle cx="0" cy="0" r="4" fill="#64748b"/>
      <text x="0" y="20" font-family="system-ui" font-size="12" fill="#64748b" text-anchor="middle">More</text>
    </g>
  </g>
</svg>