<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 640">
  <!-- Appointment scheduling preview -->
  <defs>
    <linearGradient id="calendar-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:0.3"/>
    </linearGradient>
  </defs>
  <rect width="360" height="640" fill="#ffffff"/>
  <rect x="20" y="20" width="320" height="280" rx="12" fill="url(#calendar-grad)"/>
  <!-- Add calendar grid -->
  <rect x="40" y="320" width="280" height="48" rx="8" fill="#f59e0b"/>
  <!-- Add time slots -->
  <rect x="40" y="388" width="130" height="40" rx="8" fill="#fef3c7"/>
  <rect x="190" y="388" width="130" height="40" rx="8" fill="#fef3c7"/>
</svg> 