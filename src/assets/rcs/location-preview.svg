<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 360 640">
  <!-- Location services preview with map -->
  <defs>
    <linearGradient id="map-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:0.3"/>
    </linearGradient>
  </defs>
  <rect width="360" height="640" fill="#ffffff"/>
  <rect x="20" y="20" width="320" height="320" rx="12" fill="url(#map-grad)"/>
  <!-- Add map markers and navigation elements -->
  <circle cx="180" cy="180" r="8" fill="#10b981"/>
  <rect x="40" y="360" width="280" height="48" rx="24" fill="#10b981"/>
</svg> 